#!/bin/bash

# Fly.io Deployment Script for 01TalentKE Website
# This script helps deploy your Django application to Fly.io

set -e

echo "🚀 01TalentKE Website - Fly.io Deployment Script"
echo "================================================"

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl is not installed. Please install it first:"
    echo "   curl -L https://fly.io/install.sh | sh"
    echo "   Or visit: https://fly.io/docs/getting-started/installing-flyctl/"
    exit 1
fi

# Check if user is logged in
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ You are not logged in to Fly.io"
    echo "   Please run: flyctl auth login"
    exit 1
fi

echo "✅ flyctl is installed and you are logged in"

# Check if fly.toml exists
if [ ! -f "fly.toml" ]; then
    echo "❌ fly.toml not found in current directory"
    echo "   Make sure you're in the project root directory"
    exit 1
fi

echo "✅ fly.toml found"

# Function to initialize Fly.io app
init_app() {
    echo "🏗️ Initializing Fly.io application..."

    # Check if app already exists
    if flyctl status &> /dev/null; then
        echo "✅ App already exists"
        return 0
    fi

    echo "Creating new Fly.io application..."
    flyctl launch --no-deploy --copy-config --name 01talentke-website
    echo "✅ App initialized successfully"
}

# Function to set secrets
set_secrets() {
    echo "🔐 Setting up secrets..."

    # Ensure app exists first
    if ! flyctl status &> /dev/null; then
        echo "❌ App doesn't exist. Run init_app first."
        return 1
    fi

    # Prompt for required secrets
    read -p "Enter your Django SECRET_KEY: " SECRET_KEY
    read -p "Enter your database name (or press Enter for default): " DB_NAME
    DB_NAME=${DB_NAME:-01talent}

    read -p "Enter your database user (or press Enter for default): " DB_USER
    DB_USER=${DB_USER:-postgres}

    read -s -p "Enter your database password: " DB_PASSWORD
    echo

    read -p "Enter allowed hosts (comma-separated, or press Enter for default): " ALLOWED_HOSTS
    ALLOWED_HOSTS=${ALLOWED_HOSTS:-"localhost,127.0.0.1"}

    # Set secrets
    flyctl secrets set \
        SECRET_KEY="$SECRET_KEY" \
        DB_NAME="$DB_NAME" \
        DB_USER="$DB_USER" \
        DB_PASSWORD="$DB_PASSWORD" \
        ALLOWED_HOSTS="$ALLOWED_HOSTS" \
        DEBUG="False"

    echo "✅ Secrets set successfully"
}

# Function to create PostgreSQL database
create_database() {
    echo "🗄️ Setting up PostgreSQL database..."
    
    # Check if postgres app already exists
    if flyctl postgres list | grep -q "01talentke-db"; then
        echo "✅ PostgreSQL database already exists"
    else
        echo "Creating new PostgreSQL database..."
        flyctl postgres create --name 01talentke-db --region iad
        
        # Attach database to app
        flyctl postgres attach 01talentke-db
        echo "✅ PostgreSQL database created and attached"
    fi
}

# Function to create volume for media files
create_volume() {
    echo "💾 Setting up persistent volume for media files..."
    
    # Check if volume already exists
    if flyctl volumes list | grep -q "01talentke_media"; then
        echo "✅ Volume already exists"
    else
        echo "Creating volume for media files..."
        flyctl volumes create 01talentke_media --region iad --size 1
        echo "✅ Volume created"
    fi
}

# Main deployment function
deploy() {
    echo "🚀 Deploying to Fly.io..."
    
    # Deploy the application
    flyctl deploy --build-arg BUILDKIT_INLINE_CACHE=1
    
    echo "✅ Deployment completed!"
    echo ""
    echo "🌐 Your application should be available at:"
    echo "   https://01talentke-website.fly.dev"
    echo ""
    echo "📊 Useful commands:"
    echo "   flyctl logs        - View application logs"
    echo "   flyctl ssh console - SSH into your app"
    echo "   flyctl status      - Check app status"
}

# Main script logic
case "${1:-deploy}" in
    "init")
        init_app
        ;;
    "secrets")
        set_secrets
        ;;
    "database")
        create_database
        ;;
    "volume")
        create_volume
        ;;
    "full")
        init_app
        set_secrets
        create_database
        create_volume
        deploy
        ;;
    "deploy")
        deploy
        ;;
    *)
        echo "Usage: $0 [init|secrets|database|volume|full|deploy]"
        echo ""
        echo "Commands:"
        echo "  init      - Initialize Fly.io application"
        echo "  secrets   - Set up application secrets"
        echo "  database  - Create PostgreSQL database"
        echo "  volume    - Create persistent volume"
        echo "  full      - Run all setup steps and deploy"
        echo "  deploy    - Deploy application (default)"
        exit 1
        ;;
esac

# flyctl launch added from .gitignore
# Virtual Environment
**/venv
**/env
**/myenv
**/.env

# Static files
**/staticfiles
**/**/static/dist
**/**/static/build

# Python
**/__pycache__
**/*.py[cod]
**/*.so

# Node
**/node_modules
**/01talentweb/frontend/dist

# IDE
**/.vscode
**/.idea

# Database
**/*.sqlite3
**/media

**/runme.md

**/.env.docker
**/.env.prod

# Docker
**/.docker
**/docker-compose.override.yml
**/docker-compose.yml
!**/docker-compose.yml.example

# flyctl launch added from .venv/.gitignore
.venv/**/*
fly.toml

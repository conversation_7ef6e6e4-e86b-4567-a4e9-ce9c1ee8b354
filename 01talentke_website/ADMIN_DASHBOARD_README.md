# Admin Dashboard

A simple admin dashboard for managing the 01talentke website content and data.

## Features

- **Authentication**: Simple login/logout for admin users
- **Dashboard Overview**: Statistics and recent activity
- **CRUD Operations** for all models:
  - Talents
  - Hire Requests
  - Services
  - Contact Submissions
  - Info Models (About, Statistics, Clients, Blog)

## Setup

1. **Create a superuser** (if you haven't already):
   ```bash
   cd 01talentweb/backend
   python manage.py createsuperuser
   ```

2. **Run the development server**:
   ```bash
   python manage.py runserver
   ```

3. **Access the admin dashboard**:
   - URL: `http://localhost:8000/admin-dashboard/`
   - Login with your superuser credentials

## URL Structure

```
/admin-dashboard/
├── login/                    # Admin login page
├── logout/                   # Logout (redirects to login)
├── /                        # Main dashboard
├── talents/                 # Talent management
│   ├── /                    # List all talents
│   ├── create/              # Create new talent
│   ├── <id>/edit/           # Edit talent
│   └── <id>/delete/         # Delete talent
├── hire-requests/           # Hire request management
│   ├── /                    # List all hire requests
│   ├── <id>/edit/           # Edit hire request status
│   └── <id>/delete/         # Delete hire request
└── services/                # Service management
    ├── /                    # List all services
    ├── create/              # Create new service
    ├── <id>/edit/           # Edit service
    └── <id>/delete/         # Delete service
```

## Technology Stack

- **Backend**: Django with Inertia.js
- **Frontend**: React.js
- **Styling**: Tailwind CSS
- **Authentication**: Django's built-in authentication system

## Current Implementation Status

### ✅ Completed
- Admin authentication (login/logout)
- Dashboard overview with statistics
- Talent CRUD operations
- Hire Request management (view, edit status, delete)
- Service CRUD operations
- Responsive sidebar navigation
- Basic styling with Tailwind CSS

### 🚧 To Be Implemented
- Contact Submissions management
- Info models management (About, Statistics, Clients, Blog)
- File upload handling for images
- Advanced filtering and search
- Bulk operations
- User role management
- Enhanced security features

## Frontend Structure

The React components are organized as follows:

```
01talentweb/frontend/src/pages/AdminDashboard/
├── Login.jsx                # Login page
├── Dashboard.jsx            # Main dashboard
├── components/
│   └── AdminLayout.jsx      # Shared layout with sidebar
└── Talents/
    ├── Index.jsx            # Talent list
    ├── Create.jsx           # Create talent form
    └── Edit.jsx             # Edit talent form
```

## Security Notes

- Currently uses basic Django authentication
- Only staff users can access the admin dashboard
- CSRF protection is enabled
- After frontend integration, additional security measures should be implemented:
  - Rate limiting
  - Enhanced permission system
  - Audit logging
  - Input validation and sanitization

## Usage Instructions

### Managing Talents

1. **View Talents**: Navigate to "Talents" in the sidebar
2. **Add New Talent**: Click "Add Talent" button
3. **Edit Talent**: Click "Edit" next to any talent
4. **Delete Talent**: Click "Delete" (with confirmation)

### Managing Hire Requests

1. **View Requests**: Navigate to "Hire Requests" in the sidebar
2. **Update Status**: Click "Edit" to change request status
3. **Delete Request**: Click "Delete" (with confirmation)

### Managing Services

1. **View Services**: Navigate to "Services" in the sidebar
2. **Add New Service**: Click "Add Service" button
3. **Edit Service**: Click "Edit" next to any service
4. **Delete Service**: Click "Delete" (with confirmation)

## Development Notes

- The dashboard is built with Inertia.js for seamless Django-React integration
- All forms use CSRF protection
- Error handling is implemented for all CRUD operations
- The interface is responsive and works on mobile devices
- Frontend components are ready for further customization by the frontend team

## Next Steps

1. Complete CRUD operations for remaining models
2. Add file upload functionality
3. Implement advanced search and filtering
4. Add bulk operations
5. Enhance security features
6. Add audit logging
7. Implement user role management
8. Add data export functionality

# 🚀 Fly.io Deployment Guide for 01TalentKE Website

This comprehensive guide will help you deploy the Django + React application to Fly.io, including all the lessons learned from our deployment experience.

## 📋 Prerequisites

1. **Install flyctl** (Fly.io CLI):
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```
   Or visit: https://fly.io/docs/getting-started/installing-flyctl/

2. **Create a Fly.io account** and login:
   ```bash
   flyctl auth signup  # or flyctl auth login
   ```

3. **Verify your account** (if required by Fly.io)

## 🏗️ Architecture Overview

This application consists of:
- **Backend**: Django with Inertia.js
- **Frontend**: React with Vite build system
- **Styling**: Tailwind CSS
- **Database**: PostgreSQL (managed by Fly.io)
- **Static Files**: Served via WhiteNoise

## 🔧 Pre-Deployment Setup

### 1. Configuration Files

The following files have been optimized for Fly.io deployment:

- `fly.toml` - Fly.io application configuration with health checks
- `01talentweb/Dockerfile` - Multi-stage build for frontend and backend
- `01talentweb/entrypoint.sh` - Production-ready startup script
- `01talentweb/.dockerignore` - Optimized build context
- `01talentweb/backend/zone01web/settings.py` - Production settings with CSRF fixes

### 2. Key Configuration Changes Made

#### Django Settings (`settings.py`)
- ✅ **CSRF_TRUSTED_ORIGINS** - Fixed CSRF verification for admin
- ✅ **Static files storage** - Changed to `CompressedStaticFilesStorage`
- ✅ **ALLOWED_HOSTS** - Configured for Fly.io domains
- ✅ **Database configuration** - Uses Fly.io PostgreSQL

#### Dockerfile Optimizations
- ✅ **Multi-stage build** - Separate frontend and backend builds
- ✅ **Tailwind CSS build** - Added explicit CSS compilation
- ✅ **Static files collection** - Runs during Docker build
- ✅ **Frontend asset copying** - Proper handling of Vite builds

## 🚀 Step-by-Step Deployment Guide

### Step 1: Clone and Navigate to Project

```bash
git clone <your-repository-url>
cd 01talentke_website
```

### Step 2: Initialize Fly.io App

```bash
flyctl launch --no-deploy
```

**Important**: When prompted:
- Choose app name: `01talentke-website` (or your preferred name)
- Choose region: `iad` (or closest to your users)
- Don't deploy immediately (we need to set up database first)

### Step 3: Create PostgreSQL Database

```bash
# Create the database
flyctl postgres create --name 01talentke-db --region iad

# Attach it to your app
flyctl postgres attach 01talentke-db
```

**⚠️ Critical Issue Resolution**: If you get a "failed to attach" error:

```bash
# Check if DATABASE_URL was set
flyctl secrets list

# If DATABASE_URL is missing, manually set it:
# Get the connection string from the database
flyctl postgres connect -a 01talentke-db

# Then set it manually (replace with your actual connection string)
flyctl secrets set DATABASE_URL="******************************************/database"
```

### Step 4: Set Required Environment Variables

```bash
flyctl secrets set \
  SECRET_KEY="your-django-secret-key-here" \
  ALLOWED_HOSTS="localhost,127.0.0.1,01talentke-website.fly.dev" \
  DEBUG="False"
```

**Generate a secure SECRET_KEY**:
```python
# Run this in Python to generate a secure key
import secrets
print(secrets.token_urlsafe(50))
```

### Step 5: Create Volume for Media Files

```bash
flyctl volumes create 01talentke_media --region iad --size 1
```

### Step 6: Deploy the Application

```bash
flyctl deploy
```

**Expected Build Process**:
1. Frontend build (Node.js stage)
2. Tailwind CSS compilation
3. Backend setup (Python stage)
4. Static files collection
5. Database migrations (via release_command)

### Step 7: Handle Migration Issues (If Any)

**⚠️ If you encounter migration errors during deployment:**

```bash
# SSH into the container
flyctl ssh console

# Check migration status
python /app/manage.py showmigrations

# If you see "column already exists" errors, fake the problematic migrations:
python /app/manage.py migrate talent 0002_auto_20250528_0642 --fake
python /app/manage.py migrate talent 0003_talent_image --fake

# Verify all migrations are applied
python /app/manage.py showmigrations

# Exit the container
exit
```

### Step 8: Create Admin User

```bash
# SSH into the container
flyctl ssh console

# Create superuser
python /app/manage.py createsuperuser --noinput --username admin --email <EMAIL>

# Set password
python /app/manage.py shell -c "from django.contrib.auth.models import User; u = User.objects.get(username='admin'); u.set_password('admin123'); u.save(); print('Password set successfully')"

# Exit the container
exit
```

## 🔍 Post-Deployment Verification

### Step 9: Verify Deployment

1. **Check app status:**
   ```bash
   flyctl status
   ```

2. **View logs:**
   ```bash
   flyctl logs
   ```

3. **Test main application:**
   ```bash
   curl -I https://01talentke-website.fly.dev/
   ```

4. **Test static files:**
   ```bash
   # Test CSS
   curl -I https://01talentke-website.fly.dev/static/dist/css/app.css

   # Test JS
   curl -I https://01talentke-website.fly.dev/static/static/main-*.js
   ```

5. **Test admin interface:**
   ```bash
   curl -I https://01talentke-website.fly.dev/admin/
   ```

### Step 10: Access Your Application

- **Main Website**: `https://01talentke-website.fly.dev`
- **Admin Interface**: `https://01talentke-website.fly.dev/admin/`
  - Username: `admin`
  - Password: `admin123` (change this immediately!)

### Step 11: Security Hardening

```bash
# Change admin password (recommended)
flyctl ssh console -C "python /app/manage.py changepassword admin"

# Update ALLOWED_HOSTS if using custom domain
flyctl secrets set ALLOWED_HOSTS="yourdomain.com,01talentke-website.fly.dev"
```

## 🛠️ Common Issues and Solutions

### Issue 1: Database Attachment Failure

**Problem:** `flyctl postgres attach` fails with "failed to attach" error
**Root Cause:** Fly.io sometimes fails to automatically set the DATABASE_URL secret

**Solution:**
```bash
# 1. Check if DATABASE_URL exists
flyctl secrets list

# 2. If missing, get connection details
flyctl postgres connect -a 01talentke-db

# 3. Manually set DATABASE_URL (replace with actual values)
flyctl secrets set DATABASE_URL="******************************************/database"

# 4. Verify it's set
flyctl secrets list
```

### Issue 2: Blank Page (No Content)

**Problem:** Application loads but shows blank page
**Root Cause:** Static files (JS/CSS) not being served correctly

**Solution Applied:**
1. **Fixed static files collection** - Added `collectstatic` to Dockerfile build process
2. **Added Tailwind CSS build** - Explicit CSS compilation in frontend stage
3. **Fixed file copying** - Proper copying of built assets from frontend stage

**Verification:**
```bash
# Check if static files are accessible
curl -I https://your-app.fly.dev/static/dist/css/app.css
curl -I https://your-app.fly.dev/static/static/main-*.js
```

### Issue 3: CSRF Verification Failed (403 Error)

**Problem:** Django admin shows "CSRF verification failed" error
**Root Cause:** Missing `CSRF_TRUSTED_ORIGINS` setting for production domain

**Solution Applied:**
Added to `settings.py`:
```python
# CSRF trusted origins for production
CSRF_TRUSTED_ORIGINS = []
if ON_FLY:
    CSRF_TRUSTED_ORIGINS.extend([
        f"https://{os.getenv('FLY_APP_NAME')}.fly.dev",
        f"https://{os.getenv('FLY_APP_NAME')}.flycast",
    ])
```

### Issue 4: Static Files Storage Error (500 Error)

**Problem:** Application returns 500 error due to static files storage
**Root Cause:** `CompressedManifestStaticFilesStorage` requires manifest file

**Solution Applied:**
Changed in `settings.py`:
```python
# From:
"staticfiles": {
    "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
},

# To:
"staticfiles": {
    "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
},
```

### Issue 5: Styles Not Applied

**Problem:** HTML loads but no styling applied
**Root Cause:** Tailwind CSS not being built during deployment

**Solution Applied:**
Added to Dockerfile frontend stage:
```dockerfile
# Build Tailwind CSS
RUN npx tailwindcss -i ./static/css/main.css -o ./static/dist/css/app.css --minify

# Copy built CSS to Python stage
COPY --from=frontend-builder /app/frontend/static/dist ./frontend/static/dist
```

### Issue 6: Migration Conflicts (Database Schema Out of Sync)

**Problem:** Migration fails with "column already exists" errors
**Symptoms:**
```
django.db.utils.ProgrammingError: column "featured_order" of relation "talents" already exists
django.db.utils.ProgrammingError: column "image" of relation "talents" already exists
```

**Root Cause:** Database schema is ahead of migration tracking (columns exist but migrations not marked as applied)

**Solution:**
```bash
# 1. Check migration status
python3 manage.py showmigrations

# 2. Check what columns exist in database
python3 manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute(\"SELECT column_name FROM information_schema.columns WHERE table_name='talents'\")
columns = [row[0] for row in cursor.fetchall()]
print('Columns in talents table:', columns)
"

# 3. Fake apply migrations for columns that already exist
python3 manage.py migrate talent 0002_auto_20250528_0642 --fake
python3 manage.py migrate talent 0003_talent_image --fake

# 4. Verify all migrations are applied
python3 manage.py showmigrations
```

**Prevention:** Always run migrations in the correct order and avoid manual database changes

## 📊 Useful Commands

### Application Management
```bash
# View application logs
flyctl logs

# View real-time logs
flyctl logs -f

# SSH into your application
flyctl ssh console

# Check application status
flyctl status

# Restart application
flyctl restart

# Scale your application
flyctl scale count 2
```

### Database Management
```bash
# List databases
flyctl postgres list

# Connect to database
flyctl postgres connect -a 01talentke-db

# Check database status
flyctl status -a 01talentke-db
```

### Secrets Management
```bash
# List all secrets
flyctl secrets list

# Set a secret
flyctl secrets set KEY=value

# Set multiple secrets
flyctl secrets set KEY1=value1 KEY2=value2

# Remove a secret
flyctl secrets unset KEY
```

### Debugging Commands
```bash
# Run Django management commands
flyctl ssh console -C "python /app/manage.py shell"
flyctl ssh console -C "python /app/manage.py migrate"
flyctl ssh console -C "python /app/manage.py collectstatic --noinput"

# Check static files
flyctl ssh console -C "ls -la /app/staticfiles/"

# Test database connection
flyctl ssh console -C "python /app/manage.py dbshell"
```

## 🔄 Updates and Redeployment

### For Code Changes
```bash
# 1. Make your changes locally
# 2. Test locally (optional but recommended)
docker build -t test-app ./01talentweb
docker run -p 8000:8000 test-app

# 3. Deploy to Fly.io
flyctl deploy
```

### For Configuration Changes
```bash
# Update secrets
flyctl secrets set NEW_SECRET=value

# Update fly.toml and redeploy
flyctl deploy
```

### For Database Schema Changes
```bash
# Migrations are automatically run via release_command in fly.toml
# Just deploy and migrations will run automatically
flyctl deploy
```

## 🔧 Maintenance Tasks

### Backup Database
```bash
# Create a backup
flyctl postgres backup create -a 01talentke-db

# List backups
flyctl postgres backup list -a 01talentke-db
```

### Monitor Resources
```bash
# Check resource usage
flyctl status

# View metrics
flyctl metrics

# Check volume usage
flyctl volumes list
```

### Update Dependencies
```bash
# Update Python packages
# 1. Update requirements.txt locally
# 2. Deploy
flyctl deploy

# Update Node.js packages
# 1. Update package.json locally
# 2. Deploy
flyctl deploy
```

## 🆘 Troubleshooting Checklist

When something goes wrong:

1. **Check logs first:**
   ```bash
   flyctl logs
   ```

2. **Verify secrets are set:**
   ```bash
   flyctl secrets list
   ```

3. **Check database connection:**
   ```bash
   flyctl postgres list
   flyctl ssh console -C "python /app/manage.py dbshell"
   ```

4. **Test static files:**
   ```bash
   curl -I https://your-app.fly.dev/static/dist/css/app.css
   ```

5. **Check migration status:**
   ```bash
   flyctl ssh console -C "python /app/manage.py showmigrations"
   ```

6. **Test database tables:**
   ```bash
   flyctl ssh console -C "python /app/manage.py shell -c \"
   from django.db import connection
   cursor = connection.cursor()
   cursor.execute('SELECT table_name FROM information_schema.tables WHERE table_schema=\\'public\\'')
   tables = [row[0] for row in cursor.fetchall()]
   print('Database tables:', tables)
   \""
   ```

7. **Verify application status:**
   ```bash
   flyctl status
   ```

8. **Check Fly.io platform status:**
   - Visit: https://status.fly.io/

## 🎯 Production Checklist

Before going live:

- [ ] Change default admin password
- [ ] Set strong SECRET_KEY
- [ ] Configure custom domain (if needed)
- [ ] Set up monitoring/alerts
- [ ] Configure backup strategy
- [ ] Review security settings
- [ ] Test all functionality
- [ ] Set up SSL certificate (automatic with Fly.io)

## 📞 Support Resources

- **Fly.io Documentation**: https://fly.io/docs/
- **Fly.io Community**: https://community.fly.io/
- **Django on Fly.io**: https://fly.io/docs/django/
- **Fly.io Status**: https://status.fly.io/

## 🏆 Success Indicators

Your deployment is successful when:

✅ **Main application loads** - https://your-app.fly.dev/
✅ **Styles are applied** - CSS and Tailwind styles visible
✅ **JavaScript works** - React components interactive
✅ **Admin accessible** - https://your-app.fly.dev/admin/
✅ **Database connected** - Can create/read data
✅ **Static files served** - Images, CSS, JS load correctly

## 📝 Deployment Summary

This deployment includes:
- ✅ Django backend with Inertia.js
- ✅ React frontend with Vite
- ✅ Tailwind CSS styling
- ✅ PostgreSQL database
- ✅ Static file serving via WhiteNoise
- ✅ Production-ready security settings
- ✅ Automatic database migrations
- ✅ CSRF protection configured
- ✅ Admin interface working

**Total deployment time**: ~10-15 minutes (excluding database creation)
**Estimated cost**: ~$5-10/month for small applications

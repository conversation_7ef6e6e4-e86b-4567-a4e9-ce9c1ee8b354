# PostgreSQL Configuration - CHANGE THESE VALUES!
POSTGRES_DB=replace_with_your_db_name          # Example: myapp_db
POSTGRES_USER=replace_with_your_db_user        # Example: myapp_user
POSTGRES_PASSWORD=replace_with_secure_password  # Use a strong password!

# Django Configuration
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
SECRET_KEY=your-super-secret-key-change-this-in-production

# NOTE: Change these values in production!

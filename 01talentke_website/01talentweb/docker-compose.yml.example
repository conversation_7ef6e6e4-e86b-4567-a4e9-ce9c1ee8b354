services:
  db:
    image: postgres:15
    container_name: 01talentweb-db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=your_db_name
      - POSTGRES_USER=your_db_user
      - POSTGRES_PASSWORD=your_secure_password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U your_db_user -d your_db_name"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - app-network

  web:
    build: .
    container_name: 01talentweb-web
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
      - SECRET_KEY=your-secret-key-change-in-production
      - DB_NAME=your_db_name
      - DB_USER=your_db_user
      - DB_PASSWORD=your_secure_password
      - DB_HOST=db
      - DB_PORT=5432
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  static_volume:
  media_volume:

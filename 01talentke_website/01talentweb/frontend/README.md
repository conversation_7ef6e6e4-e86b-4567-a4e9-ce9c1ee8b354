# Running postgres using downloaded binaries

## starting postgres manually
```bash
$HOME/pgsql/bin/pg_ctl -D $HOME/pgsql/data start
```

## connect with psql
```bash
$HOME/pgsql/bin/psql -U rotieno -d postgres
```
# Run postgres on windows
```bash
$psql -h <hostname> -U <user> -d <database>
```

## Installing react icons e.g search icon
```bash
npm install react-icons
```
## Installing carousel
```bash
npm install react-responsive-carousel
```
## Installing aspect-ratio plugin
```bash
npm install -D @tailwindcss/aspect-ratio

```
## Running script for populating talents table (generates data for 90)
```bash
python manage.py shell < bulk_create_talents.py
```
## Install React-Leaflet V4 for the map
```bash
npm install react-leaflet@^4.2.0 leaflet@^1.9.0
```

## Key
```bash
 python3 -c "import secrets; print(secrets.token_urlsafe(50))"
```


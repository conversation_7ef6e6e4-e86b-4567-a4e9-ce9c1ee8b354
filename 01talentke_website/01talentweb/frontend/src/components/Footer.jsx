import React from 'react';
import { FaFacebook, FaTwitter, FaLinkedin, FaLocationDot, FaPhone, FaEnvelope } from 'react-icons/fa6';
import { Container } from './Layout.jsx';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[--color-primary-500] text-white">
      {/* Main Footer Content */}
      <Container className="py-6 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-y-8 pt-2 md:pt-4">
          {/* Column 1 - Logo/Tagline */}
          <div className="flex flex-col items-center md:items-start space-y-1 md:space-y-4">
            <img src="/static/images/white-logo.png" alt="01Talent Logo" className="h-14 md:h-19 2xl:h-20" />
            <p className="font-mono text-body-s text-[--color-primary-0] tracking-wide">Recode our World</p>

          </div>

          {/* Column 2 - Navigation/Links */}
          <div className="flex flex-row md:flex-col justify-center md:justify-start gap-x-10 md:gap-y-8 mr-10">
            <div className="text-center md:text-left mb-4">
            <h3 className="text-h3 font-bold font-sans text-[--color-primary-0] whitespace-nowrap">&lt; Navigation &gt;</h3>

              <ul className="space-y-1 text-body-s font-normal font-sans text-[--color-primary-0]">
                <li><a href="/info/about" className="hover:underline">About Us</a></li>
                <li><a href="/services" className="hover:underline">Services</a></li>
                <li><a href="/talent" className="hover:underline">Talent</a></li>
                <li><a href="/contact" className="hover:underline">Contact</a></li>
              </ul>
            </div>
            <div className="text-center md:text-left">
            <h3 className="text-h3 font-bold font-sans text-[--color-primary-0]">&lt; Links &gt;</h3>

              <ul className="space-y-1 text-body-s font-normal font-sans text-[--color-primary-0]">
                {/* <li><a href="/faq" className="hover:underline">FAQs</a></li> */}
                {/* <li><a href="/privacy" className="hover:underline">Privacy Policy</a></li> */}
              <li><a href="/info/about#blog" className="hover:underline">Blog</a></li>              </ul>
            </div>
          </div>

          {/* Column 3 - Contact/Social */}
          <div>
            <div className="flex flex-col space-y-2 md:space-y-6 items-center md:items-start">
              <div className="flex items-left space-x-3 text-body-s font-sans text-[--color-primary-0] max-w-max">
                <FaLocationDot className=" w-4 h-4 mt-1 flex-shrink-0" />
                <span>
                  <span className="block md:hidden">Lake Basin Mall - Kisumu - Vihiga Road</span>
                  <span className="hidden md:block">Lake Basin Mall -<br/> Kisumu - Vihiga Road</span>
                </span>
              </div>
              <div className="flex items-center space-x-3 text-body-s font-sans">
                <FaEnvelope className="w-4 h-4 text-[--color-primary-0]" />
                <a href="mailto:<EMAIL>" className="text-[--color-primary-0] hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3 text-body-s font-sans">
                <FaPhone className="w-4 h-4 text-[--color-primary-0]" />
                <a href="tel:+25412345678" className="text-[--color-primary-0] hover:text-white transition-colors">
                  +254 123 456 78
                </a>
              </div>
            </div>
            {/* Social Icons */}
            <div className="flex justify-center md:justify-start space-x-3 mt-4 md:mt-10">
              {[FaFacebook, FaLinkedin, FaTwitter].map((Icon, idx) => (
                <a 
                  key={idx} 
                  href="#" 
                  className="p-1.5 rounded-full border-2 border-white/20 text-white/80 hover:bg-white/10 transition-colors duration-200 hover:border-[--color-primary-300] hover:text-[--color-primary-300]"
                  aria-label={`${Icon.displayName} link`}
                >
                  <Icon className="w-4 h-4" />
                </a>
              ))}
            </div>
          </div>

          {/* Column 4 - Map */}
          <div className="col-span-1 md:col-span-2 space-y-2">
            <h3 className="font-bold font-sans text-h3 text-center">Our Locations</h3>
            <img
              src="/static/images/map.png"
              alt="World map showing locations"
              className="w-full h-auto"
            />
          </div>
        </div>
      </Container>

      {/* Footer Bottom */}
      
      <Container className="border-t border-white justify-center items-center py-2 md:py-4">
        <div className="w-full text-center">
          <p className="font-sans text-white/80 font-bold text-body-s">
            <span className="align-middle">&copy;</span>2025 01 Talent Kenya, All Rights Reserved.

          </p>
        </div>
      </Container>
    </footer>
  );
};

export default Footer;

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

export default function Dropdown({ title, items }) {
  const [isOpen, setOpen] = useState(false);
  return (
    <div className="relative inline-block">
      <button
        type="button"
        onClick={() => setOpen(v => !v)}
        className="flex items-center space-x-1 px-2 py-1 hover:text-blue-500"
      >
        <span>{title}</span>
        <ChevronDown className="w-4 h-4" />
      </button>

      {isOpen && (
        <>
        <ul 
          className="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md overflow-hidden z-50"
        >
          {items.map(i => (
            <li key={i.href}>
              <a 
                href={i.href}
                onClick={() => setOpen(false)}
                className="block px-4 py-2 text-black hover:bg-gray-100"
              >
                {i.label}
              </a>
            </li>
          ))}
        </ul>
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setOpen(false)}
        />
        </>
      )}
    </div>
  );
}

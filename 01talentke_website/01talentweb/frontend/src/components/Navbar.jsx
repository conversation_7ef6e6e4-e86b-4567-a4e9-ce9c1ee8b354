import { useEffect, useState } from 'react';
import { Menu, X, ChevronDown, ChevronUp } from 'lucide-react';
import Button from './Button.jsx';
import { Container } from './Layout.jsx';
import { usePage } from '@inertiajs/react';
import HireFormModal from './HireFormModal.jsx';

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [showHireModal, setShowHireModal] = useState(false);
  const [mobileServicesOpen, setMobileServicesOpen] = useState(false);

  const { url, props } = usePage();
  const isContactPage = url === '/contact' || url.includes('/contact');
  const navbar_services = props.navbar_services || {
    what_we_offer: [],
    languages: [],
    frameworks: [],
    specializations: []
  };

  useEffect(() => {
    const mainEl = document.querySelector('main');
    const handleScroll = () => {
      if (mainEl) {
        setScrolled(mainEl.scrollTop > 50);
      }
    };
    mainEl?.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => mainEl?.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setMenuOpen(!menuOpen);

  const textColorClass = (scrolled || isContactPage) ? "text-black" : "text-white";
  const logoSrc = (scrolled || isContactPage)
    ? "/static/images/01talent_hero_logo_black.svg"
    : "/static/images/white-logo.png";

  return (
    <>
      <nav className={`fixed top-0 left-0 w-full z-40 transition-all duration-300 ${scrolled || isContactPage ? "bg-white shadow-md" : "bg-transparent"}`}>
        <Container>
          <div className="flex justify-between items-center py-4 px-0 md:px-0 ultra-xl:max-w-screen">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <img src={logoSrc} alt="Logo" className="h-8 md:h-8 xl:h-12 ultra-large:h-18" />
            </div>

            {/* Desktop Menu */}
            <ul className={`hidden lg:flex space-x-8 font-medium text-body-s ${textColorClass}`}>
              <li><a href="/" className={`hover:text-blue-500 ${textColorClass}`}>Home</a></li>
              
              <li className="relative group">
                <div className="flex flex-col">
                  <div className="flex items-center gap-1 pb-2">
                    <a href="/services" className={`hover:text-blue-500 ${textColorClass}`}>Services</a>
                    <ChevronDown className={`w-4 h-4 ${textColorClass} group-hover:rotate-180 transition-transform duration-200`} />
                  </div>

                  {/* Mega Dropdown */}
                  <div className="absolute left-0 top-full w-[600px] md:w-[800px] bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition duration-200 p-6 text-black">
                    <div className="flex flex-col md:flex-row gap-8">
                      {/* Left Column */}
                      <div className="flex-1">
                        <h3 className="text-body-m border-b-2 border-blue-500 mb-4">What We Offer</h3>
                        <div className="space-y-2">
                          {navbar_services.what_we_offer.map((service, index) => (
                            <a 
                              key={index} 
                              href={`/service_template?service=${service.name}`} 
                              className="font-semibold text-body-s hover:text-blue-500 block"
                            >
                              {service.name}
                            </a>
                          ))}
                        </div>
                      </div>

                      {/* Right Column */}
                      <div className="flex-1">
                        <h3 className="text-body-m border-b-2 border-blue-500 mb-4">Technical Expertise</h3>
                        <div className="space-y-4 text-sm">
                          <div>
                            <p className="font-semibold text-body-s mb-1">Languages</p>
                            <ul className="list-disc text-body-s list-inside text-gray-700 space-y-1">
                              {navbar_services.languages.map((lang, index) => (
                                <li key={index}>
                                  <a href={`/service_template?language=${lang.name}`} className="hover:text-blue-500">
                                    {lang.name}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <p className="font-semibold text-body-s mb-1">Frameworks</p>
                            <ul className="list-disc text-body-s list-inside text-gray-700 space-y-1">
                              {navbar_services.frameworks.map((framework, index) => (
                                <li key={index}>
                                  <a href={`/service_template?framework=${framework.name}`} className="hover:text-blue-500">
                                    {framework.name}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <p className="font-semibold text-body-s mb-1">Specializations</p>
                            <ul className="list-disc text-body-s list-inside text-gray-700 space-y-1">
                              {navbar_services.specializations.map((spec, index) => (
                                <li key={index}>
                                  <a href={`/service_template?specialization=${spec.name}`} className="hover:text-blue-500">
                                    {spec.name}
                                  </a>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>

              <li><a href="/talent" className={`hover:text-blue-500 ${textColorClass}`}>Talents</a></li>
              <li><a href="/info/about" className={`hover:text-blue-500 ${textColorClass}`}>About Us</a></li>
              <li><a href="/contact" className="hover:text-blue-500">Contact Us</a></li>
            </ul>

            {/* Desktop CTA */}
            <div className="hidden lg:block">
              <Button
                className="border-[1.5px] border-[var(--color-primary-300)] text-white bg-[var(--color-primary-300)] hover:bg-[#284B81] whitespace-nowrap px-6 py-3 h-12 rounded-lg font-bold text-button-m"
                textColor={scrolled || isContactPage ? "text-black" : "text-white"}
                style={{ minWidth: 'fit-content' }}
                onClick={() => setShowHireModal(true)}
              >
                Hire Here
              </Button>
            </div>

            {/* Hamburger */}
            <div className="lg:hidden z-50" onClick={toggleMenu}>
              {menuOpen ? (
                <X className={`${textColorClass} h-8 w-8 cursor-pointer`} />
              ) : (
                <Menu className={`${textColorClass} h-8 w-8 cursor-pointer`} />
              )}
            </div>
          </div>

          {/* Mobile Menu */}
          {menuOpen && (
            <div className="lg:hidden bg-white shadow-md px-6 py-4 space-y-4 transition-all duration-300 max-w-sm">
              <ul className="flex flex-col space-y-4 font-medium text-body-s text-black">
                <li><a href="/" className="hover:text-blue-500">Home</a></li>
                <li>
                  <div className="flex items-center justify-between w-full pb-2">
                    <a href="/services" className="hover:text-blue-500">Services</a>
                    <button
                      onClick={() => setMobileServicesOpen(!mobileServicesOpen)}
                      className="hover:text-blue-500 p-1"
                    >
                      {mobileServicesOpen ? (
                        <ChevronUp className="w-4 h-4" />
                      ) : (
                        <ChevronDown className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                  {mobileServicesOpen && (
                    <div className="mt-2 pl-4 border-l border-gray-300 space-y-2">
                      <p className="text-sm font-semibold">What We Offer</p>
                      {navbar_services.what_we_offer.map((service, index) => (
                        <a key={index} href={`/service_template?service=${service.name}`} className="text-sm hover:text-blue-500 block">
                          {service.name}
                        </a>
                      ))}

                      <p className="text-sm font-semibold mt-4">Technical Expertise</p>
                      {navbar_services.languages.map((lang, index) => (
                        <a key={index} href={`/service_template?language=${lang.name}`} className="text-sm hover:text-blue-500 block">
                          {lang.name}
                        </a>
                      ))}
                      {navbar_services.frameworks.map((framework, index) => (
                        <a key={index} href={`/service_template?framework=${framework.name}`} className="text-sm hover:text-blue-500 block">
                          {framework.name}
                        </a>
                      ))}
                      {navbar_services.specializations.map((spec, index) => (
                        <a key={index} href={`/service_template?specialization=${spec.name}`} className="text-sm hover:text-blue-500 block">
                          {spec.name}
                        </a>
                      ))}
                   
                    </div>
                  )}
                </li>
                <li><a href="/talent" className="hover:text-blue-500">Talents</a></li>
                <li><a href="/info/about" className="hover:text-blue-500">About Us</a></li>
                <li><a href="/contact" className="hover:text-blue-500">Contact Us</a></li>
              </ul>
            </div>
          )}
        </Container>
      </nav>

      {/* Hire Form Modal */}
      <HireFormModal
        isOpen={showHireModal}
        onClose={() => setShowHireModal(false)}
      />
    </>
  );
}

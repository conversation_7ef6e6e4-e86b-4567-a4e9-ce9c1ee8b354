import Button from './Button.jsx';
import { Container } from './Layout.jsx';

export default function Hero({
  desktopBg,
  mobileBg,
  title,
  description1,
  description2,
  button = null,
  secondButton = null,
  chart = null,
  icons = null,
  
  chartClass = "",
  iconsClass = "",

  chartSize = "25%",
  iconsSize = "20%",
  textContainerMaxWidth = "xs:max-w-xs md:max-w-[50%] lg:max-w-2xl xl:max-w-3xl 2xl:max-w-8xl ultra-large:max-w-8xl",
  height = "h-[100vh]",
  buttonSpacing = "mt-20",
  showSideIcons = false,
  sideIcon1 = null,
  sideIcon2 = null,
  showCirclesDecoration = false,

  //  NEW PROPS for split chart
  template_chart_shapes = null,
  template_chart_web = null,
  template_icon = null,
  templateChartShapesSize = "30%",
  templateChartWebSize = "30%",
  templateIconSize = "20%",

  templateChartShapesClass = "",
  templateChartWebClass = "",
  templateIconClass = "",
}) {
  return (
    <section className={`relative ${height} overflow-visible`}>
      {/*  Background images */}
      <img
        src={desktopBg}
        alt="Team"
        className="hidden md:block absolute inset-0 w-full h-full object-cover object-[center_10%] z-0"
      />
      <img
        src={mobileBg}
        alt="Team Mobile"
        className="block md:hidden absolute inset-0 w-full h-full object-cover object-center z-0"
      />

      {/*  Decorative circles */}
      {showCirclesDecoration && (
        <div className="xs:h-[clamp(300px,50vh,900px)] xs:top-1/4 md:block absolute right-0 md:top-1/2 mt-24 transform -translate-y-1/2 md:h-[clamp(600px,50vh,900px)] z-20">
          <img
            src="/static/images/hero-right-circles.svg"
            alt="Decorative circles"
            className="w-full h-full object-contain"
          />
        </div>
      )}

      {/*  Chart section */}
      {chart && (
        <div className="hidden md:flex absolute inset-0 z-5 top-20 justify-end pr-20 mr-20">
          <img
            src={chart}
            alt="Chart"
            className="h-[70%] w-auto"
            style={{
              maxWidth: chartSize,
              objectFit: 'contain',
              opacity: 0.8,
            }}
          />
        </div>
      )}

      {/*  Shared wrapper for shapes + web + icon */}
      {(template_chart_shapes || template_chart_web || template_icon) && (
        <div className="hidden md:flex absolute inset-0 justify-end items-center pr-20 mr-20">
          {/*  Shapes behind overlay */}
          {template_chart_shapes && (
            <div className="absolute z-5 flex items-center justify-center">
              <img
                src={template_chart_shapes}
                alt="Template Chart Shapes"
                className="w-full h-auto object-contain"
                style={{
                  maxWidth: templateChartShapesSize,
                  width: '100%',
                }}
              />
            </div>
          )}

          {/*  Web + icon above overlay */}
          {(template_chart_web || template_icon) && (
            <div className="relative z-20 flex items-center justify-center">
              {template_chart_web && (
                <img
                  src={template_chart_web}
                  alt="Template Chart Web"
                  className="relative"
                  style={{
                    maxWidth: templateChartWebSize,
                    width: '100%',
                  }}
                />
              )}
              {template_icon && (
                <img
                  src={template_icon}
                  alt="Template Icon"
                  className="absolute"
                  style={{
                    maxWidth: templateIconSize,
                    width: '100%',
                  }}
                />
              )}
            </div>
          )}
        </div>
      )}

      {/*  Overlay */}
      <div className="hidden md:block absolute inset-0 bg-gradient-to-t from-black/95 via-black/50 to-black/45 z-10" />
      <div className="block md:hidden absolute inset-0 bg-gradient-to-t from-black via-black/75 to-transparent z-10" />

      {/*  Additional icons */}
      {icons && (
        <div className="hidden md:flex absolute inset-0 z-20 top-24 justify-end pr-24 mr-24">
          <img
            src={icons}
            alt="Icons"
            className="h-[60%] w-auto"
            style={{
              maxWidth: iconsSize,
              objectFit: 'contain',
            }}
          />
        </div>
      )}

      {/*  Content */}
      <div className="relative z-20 flex items-center h-full text-white">
          <div className="container  px-0 flex flex-col md:flex-row items-center justify-between">
          <Container>

            <div className={`${textContainerMaxWidth} text-left mb-10 md:mb-0 relative`}>
              <h1 className="text-h1 font-bold mb-8">{title}</h1>
              <p className="text-body-l mb-6">{description1}</p>
              <p className="text-body-l mb-6">{description2}</p>

              {(button || secondButton || showSideIcons) && (
                <div className={`relative ${buttonSpacing} flex flex-col md:flex-row justify-between items-start md:items-center gap-4`}>
                  <div className="flex items-center gap-4">
                    {button && <div>{button}</div>}

                    {showSideIcons && (
                      <div className="flex gap-3">
                        {sideIcon1 && (
                          <a href={sideIcon1.href} className="w-12 h-[70px] md:w-14 rounded-lg bg-[#0063F9] flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-200">
                            <img src={sideIcon1.src} alt={sideIcon1.alt} className="w-5 h-5" />
                          </a>
                        )}
                        {sideIcon2 && (
                          <a href={sideIcon2.href} className="w-12 h-[70px] md:w-14  rounded-lg bg-[#0063F9] flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-200">
                            <img src={sideIcon2.src} alt={sideIcon2.alt} className="w-5 h-5 md:w-6 md:h" />
                          </a>
                        )}
                      </div>
                    )}
                  </div>

                  {secondButton && (
                    <div className="md:absolute md:right-0">{secondButton}</div>
                  )}
                </div>
              )}
            </div>

            {chart && <div className="hidden md:block md:w-[40%]"></div>}
            </Container>

          </div>
      </div>
    </section>
  );
}

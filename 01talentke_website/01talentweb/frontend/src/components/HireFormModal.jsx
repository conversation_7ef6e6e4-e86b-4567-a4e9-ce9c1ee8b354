import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';
import { useForm } from '@inertiajs/react';

const HireFormModal = ({ isOpen, onClose, talent = null }) => {
  const { data, setData, post, processing, errors, reset } = useForm({
    first_name: '',
    last_name: '',
    email: '',
    country_code: '+254',
    phone: '',
    company: '',
    subject: talent ? `Project with ${talent.name}` : '',
    description: '',
    talent_id: talent?.id || null
  });

  useEffect(() => {
    if (isOpen) {
      reset({
        first_name: '',
        last_name: '',
        email: '',
        country_code: '+254',
        phone: '',
        company: '',
        subject: talent ? `Project with ${talent.name}` : '',
        description: '',
        talent_id: talent?.id || null
      });
    }
  }, [isOpen, talent]);

  const handleSubmit = (e) => {
    e.preventDefault();
    post('/hire/api/hire-request/', {
      onSuccess: () => {
        onClose();
        alert('Your hire request has been submitted successfully!');
      }
    });
  };

  if (!isOpen || typeof window === 'undefined') return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0 z-50 flex items-start justify-center p-4 overflow-y-auto">
      <div
        className="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm"
        onClick={onClose}
      />

      <div className="relative z-10 bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">
              {talent ? `Hire ${talent.name}` : 'Hire Talent'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              &times;
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <div className="flex items-center pb-3 mb-4 border-b border-[--color-primary-800]">
                <span className="flex items-center justify-center w-6 h-6 rounded-full bg-[--color-primary-500] text-white text-xs font-semibold mr-2">1</span>
                <h3 className="text-s font-bold text-[--color-primary-800]">Personal Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label htmlFor="first_name" className="block text-s font-medium text-gray-700 mb-1">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="first_name"
                    value={data.first_name}
                    onChange={(e) => setData('first_name', e.target.value)}
                    required
                    placeholder="Philip"
                    className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                  />
                  {errors.first_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.first_name}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="last_name" className="block text-s font-medium text-gray-700 mb-1">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="last_name"
                    value={data.last_name}
                    onChange={(e) => setData('last_name', e.target.value)}
                    required
                    placeholder="Ouma"
                    className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                  />
                  {errors.last_name && (
                    <p className="text-red-500 text-xs mt-1">{errors.last_name}</p>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-s font-medium text-gray-700 mb-1">
                  Email Address <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  id="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  required
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label htmlFor="phone" className="block text-s font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <div className="flex rounded-md shadow-sm">
                  <select
                    value={data.country_code}
                    onChange={(e) => setData('country_code', e.target.value)}
                    className="px-3 py-3 border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-s rounded-l-md"
                  >
                    <option value="+1">+1 (US/CA)</option>
                    <option value="+44">+44 (UK)</option>
                    <option value="+254">+254 (KE)</option>
                    <option value="+255">+255 (TZ)</option>
                    <option value="+256">+256 (UG)</option>
                    <option value="+27">+27 (ZA)</option>
                    <option value="+233">+233 (GH)</option>
                    <option value="+234">+234 (NG)</option>
                    <option value="+91">+91 (IN)</option>
                    <option value="+86">+86 (CN)</option>
                  </select>
                  <input
                    type="tel"
                    id="phone"
                    value={data.phone}
                    onChange={(e) => setData('phone', e.target.value)}
                    placeholder="7XX XXX XXX"
                    className="flex-1 min-w-0 block w-full px-4 py-3 border border-l-0 border-gray-300 rounded-r-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                  />
                </div>
                {errors.phone && (
                  <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
                )}
              </div>
            </div>

            <div>
              <div className="flex items-center pb-3 mb-4 border-b-[3px] border-[--color-primary-800]">
                <span className="flex items-center justify-center w-6 h-6 rounded-full bg-[--color-primary-500] text-white text-xs font-semibold mr-2">2</span>
                <h3 className="text-s font-bold text-[--color-primary-800]">Project Details</h3>
              </div>

              <div className="mb-4">
                <label htmlFor="company" className="block text-s font-medium text-gray-700 mb-1">
                  Company / Organization
                </label>
                <input
                  type="text"
                  id="company"
                  value={data.company}
                  onChange={(e) => setData('company', e.target.value)}
                  placeholder="Your Company Name (Optional)"
                  className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                />
                {errors.company && (
                  <p className="text-red-500 text-xs mt-1">{errors.company}</p>
                )}
              </div>

              <div className="mb-4">
                <label htmlFor="subject" className="block text-s font-medium text-gray-700 mb-1">
                  Project Subject <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="subject"
                  value={data.subject}
                  onChange={(e) => setData('subject', e.target.value)}
                  required
                  placeholder="Briefly describe your project"
                  className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500]"
                />
                {errors.subject && (
                  <p className="text-red-500 text-xs mt-1">{errors.subject}</p>
                )}
              </div>

              <div>
                <label htmlFor="description" className="block text-s font-medium text-gray-700 mb-1">
                  Project Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  required
                  placeholder="Tell us more about your project requirements, timeline, and any specific needs..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-md text-s text-gray-800 focus:ring-[--color-primary-500] focus:border-[--color-primary-500] h-24 resize-none"
                />
                {errors.description && (
                  <p className="text-red-500 text-xs mt-1">{errors.description}</p>
                )}
              </div>
            </div>

            <button
              type="submit"
              disabled={processing}
              className="w-full py-3 bg-[--color-primary-500] text-white font-semibold rounded-md hover:bg-[--color-primary-600] transition disabled:opacity-50"
            >
              {processing ? 'Sending...' : 'Send Project Inquiry'}
            </button>
          </form>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default HireFormModal;

import { useState } from 'react';
import { FaLinkedin, FaGithub, FaGlobe, FaFileAlt } from 'react-icons/fa';
import { X } from 'lucide-react';
import HireFormModal from './HireFormModal.jsx'; 


export default function ProfileCard({ developer, onClose }) {
  const [showFullAbout, setShowFullAbout] = useState(false);
  const [showHireModal, setShowHireModal] = useState(false); 
  

  // Extract data from developer object
  const name = developer?.name || 'Unknown Developer';
  const role = developer?.profile?.role || 'Developer';
  const bio = developer?.profile?.bio || 'No bio available';
  const isAvailable = developer?.profile?.is_available !== false;
  const location = developer?.profile?.location || 'Kisumu';
  const githubUrl = developer?.profile?.github;
  const linkedinUrl = developer?.profile?.linkedin;
  const portfolioUrl = developer?.profile?.portfolio;
  const articlesUrl = developer?.profile?.articles;
  const profileImage = developer?.image;
  const techStack = developer?.profile?.tech_stack || developer?.skills || [];

  // Get initials for fallback image
  const initials = name ? name.split(' ').map(n => n[0]).join('').toUpperCase() : '?';

  // Function to extract YouTube video ID from URL
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  // Get video thumbnail URL
  const getVideoThumbnail = () => {
    const youtubeUrl = developer?.profile?.youtube;

    if (youtubeUrl) {
      const videoId = getYouTubeVideoId(youtubeUrl);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
      }
    }

    // Fallback to custom thumbnail or default
    return developer?.profile?.video_thumbnail || "../../../static/images/youtube.png";
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm p-4"
      onClick={onClose}
    >
      <div
        className="w-full max-w-3xl mx-auto bg-[#EFF7FF] rounded-xl shadow-2xl overflow-y-auto scrollbar-hide flex flex-col relative"
        style={{
          maxHeight: '95vh',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE10+
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-2 right-2 md:top-3 md:right-3 z-10 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 transition-colors"
        >
          <X className="w-5 h-5 text-gray-600" />
        </button>

        {/* Profile content */}
        <div className="p-4 sm:p-6 flex-1 flex flex-col min-w-0">
          {/* Profile Header */}
          <div className="flex flex-col md:flex-row items-center md:items-start mb-6 gap-4">
            {/* Profile image with semi-circle */}
            <div className="relative shrink-0">
              <div className="absolute -inset-1.5">
                <svg
                  viewBox="0 0 100 100"
                  className="w-full h-full"
                  style={{ transform: 'rotate(-135deg)', overflow: 'visible' }}
                >
                  <circle
                    cx="50"
                    cy="50"
                    r="48"
                    fill="none"
                    stroke="#3b82f6"
                    strokeWidth="3"
                    strokeDasharray="235 78"
                  />
                </svg>
              </div>
              {profileImage ? (
                <img
                  src={profileImage}
                  alt={`${name} Profile`}
                  className="relative w-24 h-24 md:w-28 md:h-28 rounded-full object-cover border-2 border-black"
                />
              ) : (
                <div className="relative w-24 h-24 md:w-28 md:h-28 rounded-full bg-blue-200 border-2 border-black flex items-center justify-center">
                  <span className="text-blue-800 text-xl md:text-2xl font-bold">{initials}</span>
                </div>
              )}
            </div>

            {/* Personal info */}
            <div className="flex-1 w-full">
              <div className="flex flex-col md:flex-row md:items-center justify-between w-full gap-2">
                <div className="text-center md:text-left">
                  <h2 className="text-h3 font-bold">{name}</h2>
                  <p className="text-gray-600 mb-1">{role}</p>
                  <p className="text-blue-600 text-xs">
                    {location} · <span className={isAvailable ? "text-green-600" : "text-red-600"}>
                      {isAvailable ? 'Available' : 'Not Available'}
                    </span>
                  </p>
                </div>
                <button
                  onClick={isAvailable ? () => setShowHireModal(true) : undefined}
                  disabled={!isAvailable}
                  className={`mt-3 md:mt-0 px-6 py-2 rounded-lg text-sm transition ${
                    isAvailable
                      ? 'bg-blue-600 hover:bg-blue-700 text-white cursor-pointer'
                      : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  }`}
                  title={isAvailable ? 'Hire this talent' : 'This talent is currently not available'}
                >
                  {isAvailable ? 'Hire' : 'Hire'}
                </button>
              </div>

              {/* Social links */}
              <div className="flex flex-wrap gap-4 mt-4 text-blue-600 text-sm">
                {linkedinUrl && (
                  <a href={linkedinUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 hover:text-blue-800">
                    <FaLinkedin /> LinkedIn
                  </a>
                )}
                {githubUrl && (
                  <a href={githubUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 hover:text-gray-800">
                    <FaGithub /> Github
                  </a>
                )}
                {portfolioUrl && (
                  <a href={portfolioUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 hover:text-green-600">
                    <FaGlobe /> Portfolio
                  </a>
                )}
                {articlesUrl && (
                  <a href={articlesUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 hover:text-red-500">
                    <FaFileAlt /> Articles
                  </a>
                )}
              </div>
            </div>
          </div>

          {/* About Section */}
          <div className="mb-6">
            <h3 className="text-md font-semibold mb-1">About</h3>
            <div className="text-gray-700 text-sm leading-relaxed">
              {showFullAbout ? (
                <div className="break-words whitespace-pre-wrap">
                  {bio}{' '}
                  <span
                    className="text-blue-600 cursor-pointer ml-1 text-xs hover:underline"
                    onClick={() => setShowFullAbout(false)}
                  >
                    (see less)
                  </span>
                </div>
              ) : (
                <div className="break-words">
                  {bio.length > 100 ? bio.substring(0, 100) + '...' : bio}
                  {bio.length > 100 && (
                    <span
                      className="text-blue-600 cursor-pointer ml-1 text-xs hover:underline"
                      onClick={() => setShowFullAbout(true)}
                    >
                      (see more)
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Skills */}
          <div className="space-y-6 mb-6">
            {techStack.length > 0 && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="text-sm font-semibold mb-2">Technical Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {techStack.map((skill, i) => (
                    <span
                      key={i}
                      className="bg-white border border-gray-300 px-3 py-1 rounded-full text-xs text-gray-700"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Experience and Work Type */}
            {(developer?.profile?.years_of_experience || developer?.profile?.preferred_work_type) && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="text-sm font-semibold mb-2">Experience & Preferences</h4>
                <div className="flex flex-wrap gap-2">
                  {developer?.profile?.years_of_experience && (
                    <span className="bg-white border border-gray-300 px-3 py-1 rounded-full text-xs text-gray-700">
                      {developer.profile.years_of_experience} years experience
                    </span>
                  )}
                  {developer?.profile?.preferred_work_type && (
                    <span className="bg-white border border-gray-300 px-3 py-1 rounded-full text-xs text-gray-700">
                      {developer.profile.preferred_work_type}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Video/Portfolio Section - Show if video URL or YouTube link exists */}
          {(developer?.profile?.video_url || developer?.profile?.youtube) && (
            <div className="mb-6 text-center w-full">
              <h3 className="text-md font-semibold mb-2">{`Hear from ${name}`}</h3>
              <div className="aspect-w-16 aspect-h-9 w-full rounded-lg overflow-hidden">
                <a
                  href={developer?.profile?.youtube || developer?.profile?.video_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="relative block group"
                >
                  <img
                    src={getVideoThumbnail()}
                    alt={`${name} Portfolio Video`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-50 transition">
                    <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                      <svg
                        className="w-5 h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                        />
                      </svg>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          )}

          {/* Additional Information */}
          {developer?.profile?.previous_company && (
            <div className="mb-6">
              <h3 className="text-md font-semibold mb-1">Previous Experience</h3>
              <p className="text-gray-700 text-sm">
                Previously worked at <span className="font-medium">{developer.profile.previous_company}</span>
              </p>
            </div>
          )}
        </div>
      </div>
       {/* Hire Form Modal */}
      <HireFormModal
        isOpen={showHireModal}
        talent ={developer}
        onClose={() => setShowHireModal(false)}
      />
    </div>
  );
}

import React, { useState } from 'react'; 
import { usePage } from '@inertiajs/react';
import Hero from '../../components/Hero.jsx';
import About from './components/About.jsx';
import CallToActionSection from './components/Cta.jsx';
import Blog from './components/Blog.jsx';
import Button from '../../components/Button.jsx';
import HireFormModal from '../../components/HireFormModal.jsx'; 
import { Link } from '@inertiajs/react';



export default function Index() {
  const { props } = usePage();
  const about = props.about || [];
  const [showHireModal, setShowHireModal] = useState(false); 


  return (
    <>
      <div className="relative">
        <Hero
          desktopBg="/static/images/about_hero_desktop.JPG"
          mobileBg="/static/images/about_hero_mobile.JPG"
          title={
            <>
              <span className='flex items-center justify-center'>ABOUT US </span><span className="text-transparent">dolor sit amet, consectetur adipiscing elit</span> 
            </>
          }
          // Updated container classes for proper centering
          textContainerMaxWidth="w-full max-w-[90vw] md:max-w-[80vw] lg:max-w-4xl xl:max-w-6xl 2xl:max-w-7xl ultra-large:max-w-8xl mx-auto px-4"
          // Add these classes to center content in Hero
          height="h-screen flex items-center justify-center"
          buttonSpacing="mt-28 pt-4"
          button={
            <div className="w-full md:w-auto"> {/* Button wrapper */}
              <Button
              onClick={() => setShowHireModal(true)} 
              style={{
                  color: '#FFFFFF',
                  padding: '22.92px 91.69px',
                  width: '100%',
                  maxWidth: '377.24px',
                  height: '75.69px',
                }}
                className="hover:bg-[#284B81] whitespace-nowrap bg-[var(--color-primary-300)] transition-colors duration-300 font-bold text-button-l"
              >
                Hire Here
              </Button>
            </div>
          }
          secondButton={
            <div className="w-full md:w-auto"> {/* Button wrapper */}
                <Link href="/contact" as="button" type="button">

                  <Button
                    onClick={() => {}}
                    style={{
                      padding: '22.92px 91.69px',
                      width: '100%',
                      maxWidth: '377.24px',
                      height: '75.69px',
                    }}
                    className="text-[#0063F9] bg-transparent border-2 border-[#0063F9] hover:bg-[var(--color-primary-300)] hover:text-white hover:border-[#0063F9] whitespace-nowrap transition-colors duration-300 font-bold text-button-l"
                  >
                    Talk To Us
                  </Button>
              </Link>

            </div>
          }
        />
        {/* Render the Hire Form Modal */}
        <HireFormModal
          isOpen={showHireModal}
          onClose={() => setShowHireModal(false)}
        />
      </div>
      <About />
      <Blog />
      <CallToActionSection />
    </>
  );
}
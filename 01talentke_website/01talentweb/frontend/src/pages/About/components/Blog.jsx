import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button.jsx';

// Mock data for blog posts
const initialBlogPosts = Array.from({ length: 18 }, (_, i) => ({
  id: i + 1,
  title: `Blog Post Title ${i + 1}`,
  description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
  imageUrl: `https://picsum.photos/400/300?random=${i}`,
  readMoreUrl: 'https://example.com/blog/post-1',
  date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleDateString(),
  author: `Author ${String.fromCharCode(65 + (i % 5))}`,
  category: ['Tech', 'Business', 'Design', 'Marketing', 'Development'][i % 5]
}));

const Blog = () => {
  const [posts, setPosts] = useState([]);
  const [visibleCount, setVisibleCount] = useState(6);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loader = useRef(null);
  const containerRef = useRef(null);

  // Load initial posts
  useEffect(() => {
    setPosts(initialBlogPosts);
  }, []);

  // Handle scroll event for infinite loading
  const handleObserver = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && !isLoading && hasMore) {
      setIsLoading(true);
      // Simulate API call delay
      setTimeout(() => {
        setVisibleCount((prevCount) => {
          const newCount = prevCount + 3;
          if (newCount >= posts.length) {
            setHasMore(false);
            return posts.length;
          }
          return newCount;
        });
        setIsLoading(false);
      }, 1000);
    }
  }, [isLoading, hasMore, posts.length]);

  // Set up intersection observer
  useEffect(() => {
    const option = {
      root: null,
      rootMargin: '20px',
      threshold: 0.1,
    };
    const observer = new IntersectionObserver(handleObserver, option);
    if (loader.current) {
      observer.observe(loader.current);
    }
    return () => observer.disconnect();
  }, [handleObserver]);

  return (
    <section id="blog" className="py-12 md:py-16 bg-[--color-primary-0]">
      <Container>
        <h2 className="text-h2 text-center font-bold text-gray-900 mb-12">
          Our <span className="text-[--color-primary-500]">Blog</span>
        </h2>
        
        <div className="relative">
          <div 
            ref={containerRef}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-h-[1000px] overflow-y-auto pr-4 scrollbar-thin scrollbar-thumb-[--color-primary-300] scrollbar-track-gray-100"
          >
          {posts.slice(0, visibleCount).map((post) => (
            <div 
              key={post.id}
              className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col h-full transform hover:-translate-y-1"
            >
              <div className="h-48 overflow-hidden relative">
                <img 
                  src={post.imageUrl} 
                  alt={post.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div className="p-6 flex flex-col flex-grow">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-500">{post.date}</span>
                </div>
                <h3 className="text-h3 font-bold text-[#0F172A] mb-3">{post.title}</h3>
                <p className="text-[#0F172A] mb-4 flex-grow line-clamp-3">
                  {post.description}
                </p>
                <div className="mt-auto pt-4 border-t border-gray-100">
                  <a 
                    href={post.readMoreUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-[--color-primary-500] !text-button-l font-medium hover:underline inline-flex items-center group"
                  >
                    Read More 
                    <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          ))}
            {/* Loading indicator */}
            {isLoading && (
              <div className="col-span-full flex justify-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[--color-primary-500]"></div>
              </div>
            )}
            
            {/* Intersection Observer Target */}
            <div ref={loader} className="h-20 col-span-full" />
            
            {/* No more posts message */}
            {!hasMore && posts.length > 0 && (
              <div className="col-span-full text-center py-6 bg-white border-t border-gray-100">
                <p className="text-gray-500">You've reached the end of our blog posts.</p>
                <button 
                  onClick={() => {
                    containerRef.current?.scrollTo({
                      top: 0,
                      behavior: 'smooth'
                    });
                  }}
                  className="mt-2 text-[--color-primary-500] hover:underline text-sm font-medium"
                >
                  Back to top
                </button>
              </div>
            )}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default Blog;
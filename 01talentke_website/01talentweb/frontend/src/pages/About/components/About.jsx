import React from 'react';
import { Container } from '../../../components/Layout.jsx';

const About = () => {
  return (
    <section className=" py-12 md:py-20 bg-white">
      <Container>
        {/* Heading */}
        {/* <h2 className="text-h2 text-center font-bold text-[--color-primary-800]">
          About <span className="text-[--color-primary-500]">Us</span>
        </h2> */}

        {/* Paragraph */}
        <p className="text-body-l text-[--color-primary-800] text-left my-8 md:my-12">
          We find fresh pools of digital talent in Kenya, provide them the greatest tech training experience for 2 years, and then place them in high-demand tech roles at scale. We build innovative and agile minds that can solve complex problems through creative application of tech.
        </p>

        {/* Image with optional overlay */}
        <div className="relative mx-auto md:mx-12">
          <img
            src="/static/images/apprentice.jpg"
            alt="Team at 01Talent Kenya"
            className="w-full rounded-lg object-cover"
          />

          {/* circle overlay pattern */}
          <div className="absolute inset-0 pointer-events-none grid grid-cols-[repeat(auto-fill,minmax(1.5rem,1fr))] auto-rows-[1.5rem] gap-1 overflow-hidden p-1">
            {Array.from({ length: 4096 }).map((_, i) => (
              <div
                key={i}
                className="w-full aspect-square bg-white bg-opacity-30 rounded-full"
              />
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default About;

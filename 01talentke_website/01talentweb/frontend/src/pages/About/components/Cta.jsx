import React, { useState } from 'react'; 
import Button from '../../../components/Button.jsx';
import { Container } from '../../../components/Layout.jsx';
import HireFormModal from '../../../components/HireFormModal.jsx'; 

export default function CallToActionSection() {

// const CallToActionSection = () => {
  const [showHireModal, setShowHireModal] = useState(false); 

  return (
    <section className="bg-[--color-primary-50] py-16">
      <Container>
        <div className="bg-white rounded-2xl shadow-md py-12 px-6 text-center mx-auto md:mx-12">
          <h2 className="text-h2 font-bold text-[--color-primary-800] mb-4">
            Ready To Start <span className="text-[--color-primary-500]">Hiring?</span>
          </h2>
          {/* <p className="text-[--color-primary-800] text-body-l font-normal mb-6">
            <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit, sed d
          </p> */}
          <div className="flex justify-center">
            <Button
                // href="/hire"
                onClick={() => setShowHireModal(true)} 
                className="!text-button-l !font-bold !px-8 !py-3"
                style={{ minWidth: 'fit-content' }}
            >
                Hire Here
            </Button>
          </div>
        </div>
         {/* Render the Hire Form Modal */}
         <HireFormModal
          isOpen={showHireModal}
          onClose={() => setShowHireModal(false)}
        />
      </Container>
    </section>
  );
};

// export default CallToActionSection;

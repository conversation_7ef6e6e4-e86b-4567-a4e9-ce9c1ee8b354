import React, { useState } from 'react'; 
import { usePage } from '@inertiajs/react';
import Hero from '../../components/Hero.jsx';
import Stats from './components/Stats.jsx';
import CompanyLogos from './components/CompanyLogos.jsx';
import UniqueTalentSection from './components/UniqueTalent.jsx';
import FeaturedProfiles from './components/FeaturedProfiles.jsx';
import Newsletter from './components/Newsletter.jsx';
import WhoWeAre from './components/WhoWeAre.jsx';
import HiringModel from './components/HiringModel.jsx';
import HiringModelMobile from './components/HiringModelMobile.jsx';
import ProfessionsSection from './components/ProfessionsSection.jsx';
import Button from '../../components/Button.jsx';
import HireFormModal from '../../components/HireFormModal.jsx'; 
import WhatsMoreSection from './components/Whatsmore.jsx';

export default function Index() {
    const { props } = usePage();
    const featured_developers = props.featured_developers || [];
    const stats = props.stats || [];
    const company_logos = props.company_logos || [];
    const [showHireModal, setShowHireModal] = useState(false); 

    return (
      <>
        <Hero
          desktopBg="/static/images/hero_team_desktop.JPG"
          mobileBg="/static/images/hero_team_mobile.JPG"
          title={<>01 TALENT KENYA</>}
          description1={
            <>Providing you with the <span className="text-[--color-hero-text]">best AI-ready tech talent</span> in Africa</>
          }
          description2={
            <>We are a software talent agency that is addressing the global need for <span className="text-[--color-hero-text]">top tech talent</span> by transforming access to high-tech jobs.
              We find fresh pools of digital talent in Kenya, provide them the greatest tech training experience for 2 years, and then place them in high-demand tech roles at scale.

            </>
          }
          showSideIcons={true}
          sideIcon1={{
            src: "/static/images/icons/message.svg",
            alt: "First Icon",
            href: "mailto:<EMAIL>" 
          }}
          sideIcon2={{
            src: "/static/images/icons/telephone.svg",
            alt: "Second Icon",
            href: "#link2"
          }}
          button={
            <Button
              onClick={() => setShowHireModal(true)} 
              className="
                w-full
                max-w-[377px] md:max-w-[377px] sm:max-w-[250px] h-[70px]
                whitespace-nowrap
                bg-[var(--color-primary-300)]
                hover:bg-[#284B81]
                transition-colors duration-300
                font-bold text-button-l
                py-6 px-20
                md:py-6 md:px-20
                sm:py-6 sm:px-8
                xs:py-6 xs:px-6
              "
            >
              Hire Our Talent
            </Button>
          }   
          showCirclesDecoration={true} 
        />

        {/* Render the Hire Form Modal */}
        <HireFormModal
          isOpen={showHireModal}
          onClose={() => setShowHireModal(false)}
        />

        <WhoWeAre />
        <CompanyLogos logos={company_logos} />
        <Stats stats={stats} />
        <UniqueTalentSection/>
        <FeaturedProfiles talents={featured_developers} />

       <div className="hidden md:block">
          <HiringModel />
        </div>

        {/* Mobile version: only visible on small screens */}
        <div className="block md:hidden">
          <HiringModelMobile />
        </div>

        {/* <HiringModel /> */}
        <WhatsMoreSection/>
        <ProfessionsSection />
        <Newsletter />
      </>
    );
}
import React from "react";
import { Container } from '../../../components/Layout.jsx';

export default function Stats({ stats = [] }) {
  return (
    <section className="bg-[#DEEBFE] py-12 md:py-16 lg:py-20">
      <Container>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="flex flex-col items-center">
              {/* Blue circle container */}
              <div 
                className="relative aspect-square rounded-full bg-[#0063F9] flex flex-col items-center justify-center mb-3"
                style={{
                  width: "clamp(8rem, 20vw, 16rem)"  // adjust as needed
                }}
              >
                {/* Value */}
                <h3 className="text-h3 font-bold text-white">
                  {stat.value}
                </h3>
                {/* Label */}
                <p className="text-body-l font-medium text-white tracking-wide mt-1 text-center px-2">
                  {stat.label}
                </p>
              </div>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}
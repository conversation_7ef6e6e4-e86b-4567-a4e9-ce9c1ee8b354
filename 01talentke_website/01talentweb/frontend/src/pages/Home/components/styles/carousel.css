/* Custom duration classes for smooth carousel transitions */
.duration-600 {
  transition-duration: 600ms;
}

/* Carousel container styles */
.carousel-container {
  overflow: hidden;
}

.carousel-track {
  will-change: transform;
  transition: transform 0.6s ease-out;
}


.carousel-card {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 600ms ease-out;
}

/* Legacy styles (keeping for backward compatibility) */
.card-wrapper {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.8s ease, opacity 0.8s ease;
    opacity: 0;
  }
  
  .card-left {
    left: 0;
    transform: translateX(-100%) translateY(-50%) scale(0.8);
    opacity: 0.5;
    z-index: 1;
  }
  
  .card-center {
    left: 50%;
    transform: translateX(-50%) translateY(-50%) scale(1);
    opacity: 1;
    z-index: 2;
  }
  
  .card-right {
    right: 0;
    transform: translateX(100%) translateY(-50%) scale(0.8);
    opacity: 0.5;
    z-index: 1;
  }
  
  .card-entering {
    animation: enterFromRight 0.8s ease forwards;
  }
  
  .card-exiting {
    animation: exitToLeft 0.8s ease forwards;
  }
  
  @keyframes enterFromRight {
    from {
      transform: translateX(150%) translateY(-50%) scale(0.8);
      opacity: 0;
    }
    to {
      transform: translateX(100%) translateY(-50%) scale(0.8);
      opacity: 0.5;
    }
  }
  
  @keyframes exitToLeft {
    from {
      transform: translateX(-100%) translateY(-50%) scale(0.8);
      opacity: 0.5;
    }
    to {
      transform: translateX(-150%) translateY(-50%) scale(0.8);
      opacity: 0;
    }
  }
  
import React, { useState, useEffect } from 'react';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button.jsx';
import HireFormModal from '../../../components/HireFormModal.jsx'; 


const ProfessionsSection = () => {
  const professions = [
    'Video Game Development',
    'Project Management',
    'IoT and Robotics',
    'Cloud Computing & DevOps',
    'Custom Software Development',
    'Legacy System Modernization',
    'Artificial Intelligence',
    'Cybersecurity',
    'Data Engineering',
    'Mobile and Web App Development',
    'Blockchain',
    'UI/UX and Product Design'
  ];
   const [showHireModal, setShowHireModal] = useState(false); 

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <Container>
        <div className="max-w-8xl mx-auto">
          {/* Title */}
          <div className="text-center mb-12">
            <h2 className="text-h2 font-bold text-gray-900 mb-4">
              A Multitude Of <span className="text-blue-600">Professions</span> Of The Future
            </h2>
          </div>

          <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-center">
            {/* Left Column: SVG Decoration */}
            <div className="flex-1 flex justify-center items-center">
              <div className="relative w-full max-w-md">
                <img
                  src="/static/images/icons/tech-icons.svg"
                  alt="Professions Decoration"
                  className="w-full h-auto object-contain"
                />
              </div>
            </div>

            {/* Right Column: Professions List */}
            <div className="flex-1 max-w-lg">
              <div className="space-y-4">
                {professions.map((profession, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {/* Blue checkmark with circular outline */}
                    <div className="w-5 h-5 rounded-full border-2 border-blue-600 flex-shrink-0 flex items-center justify-center">
                      <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    {/* Profession text */}
                    <span className="text-gray-700 text-body-l font-medium">
                      {profession}
                    </span>
                  </div>
                ))}
              </div>

             
            </div>
            
          </div>
           {/* Hire Developers Button */}
            
              <div className="flex justify-center mt-10 md:mt-20">
                <Button
                  href="/contact"
                  variant="fill"
                  onClick={() => setShowHireModal(true)} 

                  className="!font-bold text-body-l px-10 md:px-20 py-3"
                >
                  Hire Developers
                </Button>
              </div>
        </div>
         {/* Hire Form Modal */}
         <HireFormModal
            isOpen={showHireModal}
            onClose={() => setShowHireModal(false)}
          />
      </Container>
    </section>
  );
};

export default ProfessionsSection;

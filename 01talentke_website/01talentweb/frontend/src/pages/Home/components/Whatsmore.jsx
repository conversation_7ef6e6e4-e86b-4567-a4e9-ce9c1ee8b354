import React from 'react';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button.jsx';
import { Link } from '@inertiajs/react';

// Navigation helper for testability
export function navigateToContact() {
  window.location.assign('/contact');
}

const WhatsMoreSection = ({ navigate = navigateToContact }) => {
  return (
    <section className="bg-[--color-primary-50] py-10 md:py-16">
      <Container>
        <h2 className="text-h2 text-center font-bold text-gray-900">
          Whats <span className="text-[--color-primary-500]">More?</span>
        </h2>
        <p className="text-center text-body-l text-[--color-text-700] font-sans mt-4 mb-12">
          Before you engage our talent, you will be able to:
        </p>

        {/* Three Circle Items */}
        <div className="w-full mb-12 overflow-x-auto">
          <div className="flex flex-col sm:flex-row items-center justify-center min-w-max px-4 gap-y-6 sm:gap-4 md:gap-6">
              {[
                "Interview them",
                "Test Cultural Fit",
                "Assess Project Fit"
              ].map((text, idx) => (
                <React.Fragment key={idx}>
                  {idx > 0 && (
                    <div className="hidden sm:block w-12 md:w-22 h-1 bg-[--color-primary-300] flex-shrink-0" />
                  )}
                  <div className="relative w-36 h-36 lg:w-52 lg:h-52 xl:w-80 xl:h-80 bg-[--color-primary-300] rounded-full flex-shrink-0 flex items-center justify-center text-center p-2 sm:p-4">
                    <span className="text-[--color-primary-0] font-bold text-body-m font-sans">
                      {text}
                    </span>
                  </div>
                </React.Fragment>
              ))}
          </div>
        </div>

        {/* CTA Button */}
        <div className="flex justify-center mt-10 md:mt-20">
        <Link href="/contact" as="button" type="button">

          <Button
              variant="outline"
              className="!font-bold text-body-l px-10 md:px-16 py-3"
              onClick={navigate}
            >
                  Talk to Us
                
          </Button>
        </Link>

        </div>
      </Container>
    </section>
  );
};

export default WhatsMoreSection;

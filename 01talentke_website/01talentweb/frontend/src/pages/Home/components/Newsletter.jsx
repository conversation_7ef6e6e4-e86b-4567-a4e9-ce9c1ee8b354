// resources/js/components/NewsletterSection.jsx
import React, { useState } from 'react';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button.jsx';
import HireFormModal from '../../../components/HireFormModal.jsx';

const NewsletterSection = () => {
  const [showModal, setShowModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate async operation (e.g., API call)
    setTimeout(() => {
      setIsSubmitting(false);
      // Optionally, show a success message or reset form
    }, 1000);
  };

  return (
    <section className="w-full bg-[--color-primary-50] py-8 sm:py-12 md:py-16">
      <Container className="text-center">
        <h2 className="text-h2 font-bold text-[--color-primary-800] mb-4 md:mb-6">
          Subscribe To Our Monthly<br/>{' '}
          <span className="text-[--color-primary-300]">Newsletter</span>
        </h2>
        <p className="text-body-m font-normal text-[var(--color-text-muted)] max-w-3xl mx-auto">
          Stay in touch with everything Zone01 Kisumu. Sign up for our newsletter.
        </p>

        <form className="w-full max-w-[1200px] mx-auto px-4 sm:px-6 mt-8 md:mt-12" onSubmit={handleSubmit}>
          <div className="relative w-full">
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full h-28 px-4 py-2 pr-[calc(theme(spacing.20)+var(--space-8))] sm:pr-[calc(theme(spacing.32)+var(--space-8))] 
                       text-body-m
                       rounded-md border text-[var(--color-text)] bg-[var(--color-primary-50)]
                       focus:ring-2 focus:ring-[--color-primary-500] focus:border-[--color-primary-500] outline-none"
              required
              aria-label="Email address"
            />
            <Button
              type="submit"
              variant='filled'
              className="absolute right-1 top-1/2 -translate-y-1/2 !mr-3 md:!mr-6 
                       !text-button-s !font-bold !px-10 md:!px-20 !py-3"
              style={{ minWidth: 'fit-content' }}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Subscribe Now'}
            </Button>
          </div>
        </form>
      </Container>
    </section>
  );
};

export default NewsletterSection;

import React from "react";
import { Link } from '@inertiajs/react';
import { Container } from '../../../components/Layout.jsx';
import Button from "../../../components/Button.jsx";

const WhoWeAre = () => {
  return (
    <section className="relative  overflow-hidden">
  <Container>
    <div className="relative z-10 py-12 md:py-20 lg:py-24">
      {/* Section Title */}
      <h2 className="text-h2 text-center text-gray-900 font-bold mb-6 md:mb-8">
        <span className="text-blue-600">Who</span> We Are
      </h2>

      {/* Content Row - Two columns on md+ */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center">
        
        {/* Left Column - Masked Image */}
      <div className=" md:block relative mx-auto aspect-[4/3]"
          style={{ width: 'clamp(300px, 40vw, 1500px)' }}>
        <svg
          className="w-full h-full object-cover"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid meet"
          >
          <defs>
            <mask id="circleMask">
              <image
                href="/static/images/zero_one.svg"
                width="100%"
                height="100%"
                preserveAspectRatio="xMidYMid slice"
              />
            </mask>
          </defs>
          <image
            href="/static/images/who_we_are.jpg"
            width="100%"
            height="100%"
            preserveAspectRatio="xMidYMid slice"
            mask="url(#circleMask)"
          />
        </svg>
      </div>


        {/* Right Column - Text */}
        <div className="space-y-6 ">
          <p className="text-body-l text-gray-700 font-normal">
            We find fresh pools of digital talent in Kenya, provide them the best work experience in tech for 2 years, and then place them in high-demand tech roles at scale. 

          </p>
            
          <div className="flex justify-center mt-12 md:mt-16">
            
             <Link href="/info/about" as="button" type="button">
                <Button
                  variant="outline"
                  className="!font-bold text-body-l px-10 md:px-16 py-3"
                >
                      Know More
                      
                </Button>
            </Link>
          </div>
          

        </div>
      </div>
    </div>
  </Container>

</section>

  );
};

export default WhoWeAre;
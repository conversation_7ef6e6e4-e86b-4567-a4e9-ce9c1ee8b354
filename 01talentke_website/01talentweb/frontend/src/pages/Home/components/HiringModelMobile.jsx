import React from 'react';
import { Container } from '../../../components/Layout.jsx';

const HiringModel = () => {
  const features = [
    { id: 1, title: 'Flexible Deployment', description: 'You can easily deploy the best talent based on projects/limited periods of time as required' },
    { id: 2, title: 'Ready-to-Work Talent', description: 'Our adaptive hiring model grants you access top-notch tech talent that is job-ready from day one.' },
    { id: 3, title: 'Versatile Hiring Options', description: 'You can easily hire highly trained and diverse software engineers for full time, short-term, project basis' },
    { id: 4, title: 'Agile and Adaptive Talent', description: 'Agile talent - Our tech talent can adapt to any technology/language your company' }, 
    { id: 5, title: 'Curriculum Integration', description: 'You will be able to integrate your business needs to our curriculum' },
    { id: 6, title: 'Cost Savings', description: 'You will save tens of thousands of shillings in hiring, training and retaining talent.' },
    { id: 7, title: 'Low Turnover Risk', description: 'You will avoid employee turnover' },  
    
  ];

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <Container>
        <div className="max-w-8xl mx-auto">
          <h2 className="text-h2 font-bold text-gray-900 mb-12 text-center">
            Why <span className="text-blue-600">Our Hiring Model </span>Works For You
          </h2>

          {/* Added relative wrapper for absolute positioning */}
          <div className="relative flex flex-col lg:flex-row gap-2 lg:gap-16">
            {/* Left Column: Timeline with Features */}
            <div className="flex-2 relative z-10"> {/* z-10 to stay above deco */}
              <div className="flex flex-col">
                {features.map((feature, index) => (
                  <div key={feature.id} className="flex items-start gap-2">
                    {/* Timeline element */}
                    <div className="flex flex-col items-center">
                      <div className="flex items-center justify-center w-14 h-14 rounded-full bg-blue-600 text-white font-bold text-lg z-10">
                        {feature.id}
                      </div>
                      {index < features.length - 1 && (
                        <div className="w-0.5 border-l-4 border-dashed border-blue-400 flex-1 min-h-[3rem]" />
                      )}
                    </div>
                    <div className="flex-1 pt-2 pb-8">
                      <h3 className="text-h3 font-semibold text-gray-900 mb-1">{feature.title}</h3>
                      <p className="text-gray-600 text-body-s">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column: Decorative SVG stays on right on mobile */}
            <div className="flex-1.5 min-w-0">
              <div
                className="
                  absolute right-0 top-0 
                  lg:static 
                  pointer-events-none
                  opacity-30 lg:opacity-100
                "
                style={{
                  width: 'clamp(150px, 25vw, 400px)',
                  height: 'clamp(350px, 45vw, 1200px)',
                }}
              >
               

                <img
                  src="/static/images/right-deco-mobile.svg"
                  alt="Hiring Model Graphic"
                  className=" w-full  right-0 object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default HiringModel;

import React, { useEffect, useState } from 'react';
import { Link } from '@inertiajs/react';
import TalentCard from '../../../components/TalentCard.jsx';
import { Container } from '../../../components/Layout.jsx';
import './styles/carousel.css';
import Button from '../../../components/Button.jsx';
import ProfileCard from '../../../components/ProfileCard.jsx';
import HireFormModal from '../../../components/HireFormModal.jsx';
import { router } from '@inertiajs/react';

const FeaturedProfiles = ({ talents = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [selectedTalent, setSelectedTalent] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showHireModal, setShowHireModal] = useState(false);

  const maxIndex = talents.length > 0 ? talents.length - 1 : 0;

  useEffect(() => {
    if (talents.length <= 1) return;

    const interval = setInterval(() => {
      handleNext();
    }, 5000);
    return () => clearInterval(interval);
  }, [talents.length]);

  const handlePrev = () => {
    if (talents.length <= 1 || isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => (prev <= 0 ? maxIndex : prev - 1));
    setTimeout(() => setIsTransitioning(false), 600);
  };

  const handleNext = () => {
    if (talents.length <= 1 || isTransitioning) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => (prev >= maxIndex ? 0 : prev + 1));
    setTimeout(() => setIsTransitioning(false), 600);
  };

  const handleTalentClick = (talent) => {
    setSelectedTalent(talent);
    setShowModal(true);
  };

  const handleHireClick = (talent) => {
    setSelectedTalent(talent);
    setShowHireModal(true);
  };

  const extendedTalents = talents.length > 0
    ? [...talents.slice(-2), ...talents, ...talents.slice(0, 2)]
    : [];

  const getTransformOffset = () => {
    const cardWidth = window.innerWidth < 768 ? 100 : 100 / 3;
    const offset = (currentIndex + (window.innerWidth < 768 ? 0 : 2)) * cardWidth;
    return `translateX(calc(-${offset}% + ${window.innerWidth < 768 ? '0' : '50%'}))`;
  };

  const getCardClasses = (index) => {
    const isMobile = window.innerWidth < 768;
    const centerIndex = isMobile ? currentIndex : currentIndex + 2;
    const position = index - centerIndex;

    const base = 'flex justify-center items-center transition-all duration-500 ease-out';
    const mobileBase = 'w-full px-2';
    const desktopBase = 'w-1/3 px-2';

    if (isMobile) {
      return position === 0
        ? `${base} ${mobileBase} opacity-100 scale-100 z-10`
        : `${base} ${mobileBase} opacity-0 scale-90 absolute`;
    }

    switch (position) {
      case -1:
        return `${base} ${desktopBase} opacity-70 scale-75 -mr-[10rem] z-0`;
      case 0:
        return `${base} ${desktopBase} opacity-100 scale-100 z-10`;
      case 1:
        return `${base} opacity-70 scale-75 -ml-[10rem] z-0`;
      default:
        return `${base} opacity-0 scale-75 z-0 pointer-events-none`;
    }
  };

  return (
    <>
      <section className="w-full bg-[--color-primary-50]">
        <Container>
          <div className="mx-auto pt-4 md:pt-8">
            <h2 className="text-h2 font-sans text-center text-[--color-primary-800] font-bold">
              Meet Our <span className="text-[--color-primary-500]">Pool of Talent</span>
            </h2>
            <p className="text-body-l text-center font-sans py-8 md:py-12 font-normal text-[--color-primary-800]">
              Our approach is personal. Each apprentice has a unique relationship with us from the start allowing us to fully vouch for their expertise and work ethic. Come meet them, hire them, see how good they are.
            </p>
          </div>

          <div className="relative bg-[var(--color-primary-50)] py-10 md:py-0 rounded-xl md:rounded-2xl overflow-hidden">
            <div className="relative min-h-[250px] sm:min-h-[400px] md:min-h-[500px] mx-auto w-full max-w-7xl overflow-hidden">
              <div
                className="carousel-track flex duration-600 ease-out"
                style={{ transform: getTransformOffset() }}
              >
                {extendedTalents.map((talent, index) => (
                  <div
                    key={`${talent.id}-${index}`}
                    className={`flex-shrink-0 w-full md:w-1/3 ${getCardClasses(index)}`}
                  >
                    <TalentCard
                      talent={talent}
                      onClick={() => handleTalentClick(talent)}
                      onHire={() => handleHireClick(talent)}
                      variant="default"
                      showTitle={true}
                      showDescription={true}
                      showPortfolioButton={true}
                      showHireButton={true}
                      showSocialIcons={true}
                      showSkills={false}
                      showAvailability={false}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={handlePrev}
              className="absolute top-[35%] border border-black/32 transition-all duration-300
                w-[50px] h-[50px] left-2 flex items-center justify-center
                xs:w-[60px] xs:h-[60px] xs:left-3
                sm:w-[70px] sm:h-[70px] sm:left-4
                md:w-[80px] md:h-[80px] md:left-6"
              aria-label="Previous profile"
            >
              <img
                src="/static/images/rightarrow.svg"
                alt="Previous"
                className="w-6 h-6 transform rotate-180 md:w-12 md:h-12"
              />
            </button>
            <button
              onClick={handleNext}
              className="absolute top-[35%] border border-black/32 transition-all duration-300
                w-[50px] h-[50px] right-2 flex items-center justify-center
                xs:w-[60px] xs:h-[60px] xs:right-3
                sm:w-[70px] sm:h-[70px] sm:right-4
                md:w-[80px] md:h-[80px] md:right-6"
              aria-label="Next profile"
            >
              <img
                src="/static/images/rightarrow.svg"
                alt="Next"
                className="w-6 h-6 md:w-12 md:h-12"
              />
            </button>

            {/* CTA Button */}
            <div className="flex justify-center my-16">
              <Link href="/talent/#talents" as="button" type="button">
                <Button
                  className="!font-bold text-body-l px-10 md:px-16 py-3"
                  style={{ minWidth: 'fit-content' }}
                >
                  <span className="flex items-center">
                    View All Talents
                    <svg
                      className="w-4 h-4 xs:w-4 xs:h-4 sm:w-5 sm:h-5 md:w-5 md:h-5 lg:w-6 lg:h-6 xl:w-6 xl:h-6 2xl:w-6 2xl:h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                </Button>
              </Link>
            </div>
          </div>

          <div className="hidden md:block relative w-full mx-auto px-4 justify-end">
            <img
              src="/static/images/zero.png"
              alt=""
              className="absolute bottom-0 right-16 h-40 z-20"
            />
            <img
              src="/static/images/one.png"
              alt=""
              className="absolute bottom-0 right-0 h-40 z-20"
            />
          </div>
        </Container>
      </section>

      {/* Profile Modal */}
      {showModal && selectedTalent && (
        <ProfileCard
          developer={selectedTalent}
          onClose={() => setShowModal(false)}
        />
      )}

      {/* Hire Form Modal */}
      {showHireModal && (
        <HireFormModal
          isOpen={showHireModal}
          onClose={() => setShowHireModal(false)}
          talent={selectedTalent}
        />
      )}
    </>
  );
};

export default FeaturedProfiles;

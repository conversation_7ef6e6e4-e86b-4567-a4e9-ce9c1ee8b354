import React, { useState, useEffect, useRef } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button.jsx';
import HireFormModal from '../../../components/HireFormModal.jsx'; 


const UniqueTalentSection = () => {
  const [showHireModal, setShowHireModal] = useState(false); 

  return (
    <section className="bg-[--color-primary-0] relative">
      <Container className="py-12 md:py-16 lg:py-20">
        <div className="mx-auto text-center lg:text-left">
          <h2 className="text-h2 text-center font-bold">

              <span className="whitespace-normal sm:whitespace-nowrap text-[--color-primary-800]">What Makes </span>
              <span className="whitespace-normal sm:whitespace-nowrap text-[--color-primary-500]">Our Talent</span>
              <span className="whitespace-normal sm:whitespace-nowrap text-[--color-primary-800]"> Unique</span>
          </h2>
          <p className="text-[--color-primary-800] my-8 sm:my-10 md:my-12 font-sans font-normal text-body-l text-center">
            Our tech talents are fully adaptable and equipped with hard and soft skills to work in fast-moving tech environments.
            We rigorously select the best tech talent from tens of thousands of applications across Kenya.
          </p>
        </div>
    
      
      <div className="mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-start">
        {/* RIGHT COLUMN - Appears first on mobile */}
        <div className="lg:hidden relative h-[400px] md:h-[600px] overflow-hidden order-1">
          <div className="relative w-full h-full">
            <img 
              src="/static/images/josephine.jpg" 
              alt="a developer" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 overflow-hidden">
              {/* Right side image */}
              <img 
                src="/static/images/Vector.png" 
                alt="" 
                className="absolute right-0 h-full w-auto"
                style={{
                  maxWidth: 'none',
                  top: '25%',
                  height: '75%',
                  width: 'auto',
                  objectFit: 'cover'
                }}
              />
              
              {/* Left top corner image */}
              <img 
                src="/static/images/Dots.svg" 
                alt="" 
                className="absolute left-0 top-0 h-[55%] w-auto"
                style={{
                  maxWidth: 'none',
                  objectFit: 'contain',
                  transform: 'scaleX(-1)',
                }}
              />
            </div>
          </div>
        </div>

        {/* LEFT COLUMN */}
        <div className="px-4 sm:px-6 lg:px-0 order-2 lg:order-1">
          <h4 className="font-medium text-[--color-primary-800] mb-6 text-body-l tracking-normal font-sans text-center lg:text-left">
            We then train them to:
          </h4>

          {/* Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            {[
              'Code in Go, Javascript, PHP, C, C#, Python and Rust languages making our talent dynamic',
              'Design and manage projects effectively',
              'Think and solve complex problems',
              'Collaborate and succeed in diverse teams',
            ].map((item, i) => (
              <div
                key={i}
                className="w-full min-h-[180px] sm:min-h-[200px] p-6 sm:p-8 rounded-2xl sm:rounded-[29.17px] border border-[#DEDEDE] bg-[--color-primary-0] relative"
                style={{
                  boxShadow: '0px 4.81px 4.81px 0px #00000040',
                  borderWidth: '1.2px'
                }}
              >
                <h5 
                  className="absolute font-mono font-medium text-body-l"
                  style={{
                    width: '40px',
                    height: '40px',
                    top: '20px',
                    left: '20px',
                    color: '#000000'
                  }}
                >
                  0{i + 1}
                </h5>
                <p 
                  className="font-sans text-[--color-primary-800] font-normal text-body-m mt-12 sm:mt-14"
                >
                  {item}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* RIGHT COLUMN - Hidden on mobile, shown on lg screens */}
        <div className="hidden lg:block relative h-[400px] md:h-[600px] overflow-hidden order-2">
          <div className="relative w-full h-full">
            <img 
              src="/static/images/josephine.jpg" 
              alt="a developer" 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 overflow-hidden">
              {/* Right side image */}
              <img 
                src="/static/images/Vector.png" 
                alt="" 
                className="absolute right-0 h-full w-auto"
                style={{
                  maxWidth: 'none',
                  top: '25%',
                  height: '75%',
                  width: 'auto',
                  objectFit: 'cover'
                }}
              />
              
              {/* Left top corner image */}
              <img 
                src="/static/images/Dots.svg" 
                alt="" 
                className="absolute left-0 top-0 h-[55%] w-auto"
                style={{
                  maxWidth: 'none',
                  objectFit: 'contain',
                  transform: 'scaleX(-1)',
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-center mt-12 md:mt-16">
        <Button
          
          href="/hire"
          variant='filled'
          className="!font-bold text-body-l px-10 md:px-20 py-3"
          style={{ minWidth: 'fit-content' }}
          onClick={() => setShowHireModal(true)} 

        >
          Hire Developers
        </Button>
      </div>
      {/* Hire Form Modal */}
      <HireFormModal
        isOpen={showHireModal}
        onClose={() => setShowHireModal(false)}
      />
      </Container>
    </section>
  );
};

export default UniqueTalentSection;
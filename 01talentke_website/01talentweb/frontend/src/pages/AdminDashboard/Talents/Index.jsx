import React, { useState } from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const TalentsIndex = () => {
    const { props } = usePage();
    const { talents } = props;
    const [searchTerm, setSearchTerm] = useState('');

    const filteredTalents = talents.filter(talent =>
        talent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        talent.email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleDelete = async (talentId) => {
        if (confirm('Are you sure you want to delete this talent?')) {
            try {
                const response = await fetch(`/admin-dashboard/talents/${talentId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting talent: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting talent');
            }
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Talents</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage all talents in the system.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin-dashboard/talents/create/"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                            Add Talent
                        </Link>
                    </div>
                </div>

                {/* Search */}
                <div className="max-w-md">
                    <input
                        type="text"
                        placeholder="Search talents..."
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {/* Talents Table */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul className="divide-y divide-gray-200">
                        {filteredTalents.length > 0 ? (
                            filteredTalents.map((talent) => (
                                <li key={talent.id}>
                                    <div className="px-4 py-4 flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0 h-10 w-10">
                                                {talent.image ? (
                                                    <img
                                                        className="h-10 w-10 rounded-full object-cover"
                                                        src={talent.image}
                                                        alt={talent.name}
                                                    />
                                                ) : (
                                                    <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <span className="text-sm font-medium text-gray-700">
                                                            {talent.name.charAt(0)}
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {talent.name}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {talent.email}
                                                </div>
                                                {talent.profile.role && (
                                                    <div className="text-sm text-gray-500">
                                                        {talent.profile.role}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {talent.featured_order && (
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Featured #{talent.featured_order}
                                                </span>
                                            )}
                                            <Link
                                                href={`/admin-dashboard/talents/${talent.id}/edit/`}
                                                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                            >
                                                Edit
                                            </Link>
                                            <button
                                                onClick={() => handleDelete(talent.id)}
                                                className="text-red-600 hover:text-red-900 text-sm font-medium"
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            ))
                        ) : (
                            <li className="px-4 py-4 text-center text-gray-500">
                                {searchTerm ? 'No talents found matching your search.' : 'No talents found.'}
                            </li>
                        )}
                    </ul>
                </div>
            </div>
        </AdminLayout>
    );
};

export default TalentsIndex;

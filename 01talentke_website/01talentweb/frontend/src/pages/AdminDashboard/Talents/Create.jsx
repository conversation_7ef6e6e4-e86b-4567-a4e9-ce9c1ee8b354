import React, { useState } from 'react';
import { Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const TalentCreate = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        featured_order: '',
        profile: {
            role: '',
            bio: '',
            skills: [],
            linkedin: '',
            github: '',
            articles: '',
            is_available: true,
            average_rating: 4.5
        }
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [skillInput, setSkillInput] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const response = await fetch('/admin-dashboard/talents/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                router.visit(data.redirect);
            } else {
                setError(data.error || 'Failed to create talent');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        
        if (name.startsWith('profile.')) {
            const profileField = name.split('.')[1];
            setFormData(prev => ({
                ...prev,
                profile: {
                    ...prev.profile,
                    [profileField]: type === 'checkbox' ? checked : value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const addSkill = () => {
        if (skillInput.trim() && !formData.profile.skills.includes(skillInput.trim())) {
            setFormData(prev => ({
                ...prev,
                profile: {
                    ...prev.profile,
                    skills: [...prev.profile.skills, skillInput.trim()]
                }
            }));
            setSkillInput('');
        }
    };

    const removeSkill = (skillToRemove) => {
        setFormData(prev => ({
            ...prev,
            profile: {
                ...prev.profile,
                skills: prev.profile.skills.filter(skill => skill !== skillToRemove)
            }
        }));
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-xl font-semibold text-gray-900">Create New Talent</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Add a new talent to the system.
                    </p>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                                {error}
                            </div>
                        )}

                        {/* Basic Information */}
                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                                    Name *
                                </label>
                                <input
                                    type="text"
                                    name="name"
                                    id="name"
                                    required
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.name}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                                    Email *
                                </label>
                                <input
                                    type="email"
                                    name="email"
                                    id="email"
                                    required
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.email}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <label htmlFor="featured_order" className="block text-sm font-medium text-gray-700">
                                    Featured Order
                                </label>
                                <input
                                    type="number"
                                    name="featured_order"
                                    id="featured_order"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.featured_order}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <label htmlFor="profile.role" className="block text-sm font-medium text-gray-700">
                                    Role
                                </label>
                                <input
                                    type="text"
                                    name="profile.role"
                                    id="profile.role"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.profile.role}
                                    onChange={handleChange}
                                />
                            </div>
                        </div>

                        {/* Bio */}
                        <div>
                            <label htmlFor="profile.bio" className="block text-sm font-medium text-gray-700">
                                Bio
                            </label>
                            <textarea
                                name="profile.bio"
                                id="profile.bio"
                                rows={4}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                value={formData.profile.bio}
                                onChange={handleChange}
                            />
                        </div>

                        {/* Skills */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Skills</label>
                            <div className="mt-1 flex">
                                <input
                                    type="text"
                                    className="flex-1 border-gray-300 rounded-l-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    placeholder="Add a skill"
                                    value={skillInput}
                                    onChange={(e) => setSkillInput(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                                />
                                <button
                                    type="button"
                                    onClick={addSkill}
                                    className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm"
                                >
                                    Add
                                </button>
                            </div>
                            <div className="mt-2 flex flex-wrap gap-2">
                                {formData.profile.skills.map((skill, index) => (
                                    <span
                                        key={index}
                                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                    >
                                        {skill}
                                        <button
                                            type="button"
                                            onClick={() => removeSkill(skill)}
                                            className="ml-1 text-blue-600 hover:text-blue-800"
                                        >
                                            ×
                                        </button>
                                    </span>
                                ))}
                            </div>
                        </div>

                        {/* Social Links */}
                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
                            <div>
                                <label htmlFor="profile.linkedin" className="block text-sm font-medium text-gray-700">
                                    LinkedIn
                                </label>
                                <input
                                    type="url"
                                    name="profile.linkedin"
                                    id="profile.linkedin"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.profile.linkedin}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <label htmlFor="profile.github" className="block text-sm font-medium text-gray-700">
                                    GitHub
                                </label>
                                <input
                                    type="url"
                                    name="profile.github"
                                    id="profile.github"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.profile.github}
                                    onChange={handleChange}
                                />
                            </div>

                            <div>
                                <label htmlFor="profile.articles" className="block text-sm font-medium text-gray-700">
                                    Articles
                                </label>
                                <input
                                    type="url"
                                    name="profile.articles"
                                    id="profile.articles"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.profile.articles}
                                    onChange={handleChange}
                                />
                            </div>
                        </div>

                        {/* Availability */}
                        <div className="flex items-center">
                            <input
                                id="profile.is_available"
                                name="profile.is_available"
                                type="checkbox"
                                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                checked={formData.profile.is_available}
                                onChange={handleChange}
                            />
                            <label htmlFor="profile.is_available" className="ml-2 block text-sm text-gray-900">
                                Available for hire
                            </label>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/admin-dashboard/talents/"
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                {loading ? 'Creating...' : 'Create Talent'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default TalentCreate;

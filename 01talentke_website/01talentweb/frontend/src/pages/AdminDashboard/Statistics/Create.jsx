import React, { useState } from 'react';
import { Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const StatisticsCreate = () => {
    const [formData, setFormData] = useState({
        label: '',
        value: '',
        icon: ''
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const response = await fetch('/admin-dashboard/statistics/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify({
                    ...formData,
                    value: parseInt(formData.value)
                })
            });

            const data = await response.json();

            if (data.success) {
                router.visit(data.redirect);
            } else {
                setError(data.error || 'Failed to create statistic');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-xl font-semibold text-gray-900">Create New Statistic</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Add a new statistic to display on the website.
                    </p>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                                {error}
                            </div>
                        )}

                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label htmlFor="label" className="block text-sm font-medium text-gray-700">
                                    Label *
                                </label>
                                <input
                                    type="text"
                                    name="label"
                                    id="label"
                                    required
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.label}
                                    onChange={handleChange}
                                    placeholder="e.g., Projects Completed"
                                />
                            </div>

                            <div>
                                <label htmlFor="value" className="block text-sm font-medium text-gray-700">
                                    Value *
                                </label>
                                <input
                                    type="number"
                                    name="value"
                                    id="value"
                                    required
                                    min="0"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.value}
                                    onChange={handleChange}
                                    placeholder="e.g., 150"
                                />
                            </div>

                            <div className="sm:col-span-2">
                                <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
                                    Icon (optional)
                                </label>
                                <input
                                    type="text"
                                    name="icon"
                                    id="icon"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.icon}
                                    onChange={handleChange}
                                    placeholder="e.g., 🚀 or star"
                                />
                                <p className="mt-1 text-sm text-gray-500">
                                    You can use emoji or text for the icon.
                                </p>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/admin-dashboard/statistics/"
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                {loading ? 'Creating...' : 'Create Statistic'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default StatisticsCreate;

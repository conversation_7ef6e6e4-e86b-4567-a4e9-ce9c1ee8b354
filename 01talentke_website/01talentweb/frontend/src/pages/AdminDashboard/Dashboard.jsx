import React from 'react';
import { usePage, Link } from '@inertiajs/react';
import AdminLayout from './components/AdminLayout';

const Dashboard = () => {
    const { props } = usePage();
    const { stats, recent_hire_requests, recent_contacts } = props;

    const statCards = [
        { name: 'Talents', value: stats.talents, color: 'bg-blue-500', link: '/admin-dashboard/talents/' },
        { name: 'Hire Requests', value: stats.hire_requests, color: 'bg-green-500', link: '/admin-dashboard/hire-requests/' },
        { name: 'Services', value: stats.services, color: 'bg-purple-500', link: '/admin-dashboard/services/' },
        { name: 'Contact Submissions', value: stats.contact_submissions, color: 'bg-yellow-500', link: '/admin-dashboard/contacts/' },
        { name: 'About Entries', value: stats.about_entries, color: 'bg-red-500', link: '/admin-dashboard/about/' },
        { name: 'Statistics', value: stats.statistics, color: 'bg-indigo-500', link: '/admin-dashboard/statistics/' },
        { name: 'Clients', value: stats.clients, color: 'bg-pink-500', link: '/admin-dashboard/clients/' },
        { name: 'Blog Posts', value: stats.blog_posts, color: 'bg-gray-500', link: '/admin-dashboard/blogs/' },
    ];

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Welcome to the admin dashboard. Here's an overview of your system.
                    </p>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                    {statCards.map((stat) => (
                        <Link
                            key={stat.name}
                            href={stat.link}
                            className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
                        >
                            <div>
                                <div className={`absolute ${stat.color} rounded-md p-3`}>
                                    <div className="w-6 h-6 text-white">
                                        <svg fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <p className="ml-16 text-sm font-medium text-gray-500 truncate">{stat.name}</p>
                                <p className="ml-16 text-2xl font-semibold text-gray-900">{stat.value}</p>
                            </div>
                        </Link>
                    ))}
                </div>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {/* Recent Hire Requests */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Hire Requests</h3>
                            <div className="mt-5">
                                {recent_hire_requests.length > 0 ? (
                                    <div className="flow-root">
                                        <ul className="-my-5 divide-y divide-gray-200">
                                            {recent_hire_requests.map((request) => (
                                                <li key={request.id} className="py-4">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                                {request.first_name} {request.last_name}
                                                            </p>
                                                            <p className="text-sm text-gray-500 truncate">
                                                                {request.subject}
                                                            </p>
                                                        </div>
                                                        <div className="inline-flex items-center text-base font-semibold text-gray-900">
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                                request.status === 'reviewed' ? 'bg-blue-100 text-blue-800' :
                                                                request.status === 'contacted' ? 'bg-purple-100 text-purple-800' :
                                                                request.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                                'bg-red-100 text-red-800'
                                                            }`}>
                                                                {request.status}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                ) : (
                                    <p className="text-sm text-gray-500">No recent hire requests</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Recent Contact Submissions */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Contact Submissions</h3>
                            <div className="mt-5">
                                {recent_contacts.length > 0 ? (
                                    <div className="flow-root">
                                        <ul className="-my-5 divide-y divide-gray-200">
                                            {recent_contacts.map((contact) => (
                                                <li key={contact.id} className="py-4">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                                {contact.name}
                                                            </p>
                                                            <p className="text-sm text-gray-500 truncate">
                                                                {contact.message}
                                                            </p>
                                                        </div>
                                                        <div className="inline-flex items-center text-base font-semibold text-gray-900">
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                                contact.is_processed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                            }`}>
                                                                {contact.is_processed ? 'Processed' : 'Pending'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                ) : (
                                    <p className="text-sm text-gray-500">No recent contact submissions</p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default Dashboard;

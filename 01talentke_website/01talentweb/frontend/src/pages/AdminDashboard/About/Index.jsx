import React from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const AboutIndex = () => {
    const { props } = usePage();
    const { about_entries } = props;

    const handleDelete = async (aboutId) => {
        if (confirm('Are you sure you want to delete this about entry?')) {
            try {
                const response = await fetch(`/admin-dashboard/about/${aboutId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting about entry: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting about entry');
            }
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">About Content</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage the about page content.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin-dashboard/about/create/"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                            Add About Entry
                        </Link>
                    </div>
                </div>

                {/* About Entries */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    {about_entries.length > 0 ? (
                        <ul className="divide-y divide-gray-200">
                            {about_entries.map((about) => (
                                <li key={about.id}>
                                    <div className="px-4 py-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-gray-900">{about.title}</h3>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-500">
                                                        {about.content.length > 200 
                                                            ? about.content.substring(0, 200) + '...' 
                                                            : about.content}
                                                    </p>
                                                </div>
                                                {about.image && (
                                                    <div className="mt-2">
                                                        <img 
                                                            src={about.image} 
                                                            alt={about.title} 
                                                            className="h-20 w-auto object-cover rounded"
                                                        />
                                                    </div>
                                                )}
                                                <div className="mt-2 text-xs text-gray-400">
                                                    Last updated: {new Date(about.updated_at).toLocaleString()}
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 ml-4">
                                                <Link
                                                    href={`/admin-dashboard/about/${about.id}/edit/`}
                                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                                >
                                                    Edit
                                                </Link>
                                                <button
                                                    onClick={() => handleDelete(about.id)}
                                                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <div className="px-4 py-12 text-center">
                            <p className="text-gray-500">No about entries found. Create one to get started.</p>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
};

export default AboutIndex;

import React, { useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const ContactEdit = () => {
    const { props } = usePage();
    const { contact } = props;
    
    const [formData, setFormData] = useState({
        is_processed: contact.is_processed || false
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const response = await fetch(`/admin-dashboard/contacts/${contact.id}/edit/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                router.visit(data.redirect);
            } else {
                setError(data.error || 'Failed to update contact submission');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: checked
        }));
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-xl font-semibold text-gray-900">Contact Submission Details</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        View and manage contact submission.
                    </p>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    {/* Contact Details (Read-only) */}
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Name</label>
                                <p className="mt-1 text-sm text-gray-900">{contact.name}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Email</label>
                                <p className="mt-1 text-sm text-gray-900">
                                    <a href={`mailto:${contact.email}`} className="text-indigo-600 hover:text-indigo-900">
                                        {contact.email}
                                    </a>
                                </p>
                            </div>
                            {contact.phone && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                                    <p className="mt-1 text-sm text-gray-900">
                                        <a href={`tel:${contact.phone}`} className="text-indigo-600 hover:text-indigo-900">
                                            {contact.phone}
                                        </a>
                                    </p>
                                </div>
                            )}
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Submitted</label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(contact.created_at).toLocaleString()}
                                </p>
                            </div>
                            <div className="sm:col-span-2">
                                <label className="block text-sm font-medium text-gray-700">Message</label>
                                <div className="mt-1 p-3 bg-white border border-gray-200 rounded-md">
                                    <p className="text-sm text-gray-900 whitespace-pre-wrap">
                                        {contact.message}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Status Update Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                                {error}
                            </div>
                        )}

                        <div>
                            <div className="flex items-center">
                                <input
                                    id="is_processed"
                                    name="is_processed"
                                    type="checkbox"
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    checked={formData.is_processed}
                                    onChange={handleChange}
                                />
                                <label htmlFor="is_processed" className="ml-2 block text-sm text-gray-900">
                                    Mark as processed
                                </label>
                            </div>
                            <p className="mt-1 text-sm text-gray-500">
                                Check this box when you have responded to or handled this contact submission.
                            </p>
                        </div>

                        {/* Quick Actions */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Quick Actions</h4>
                            <div className="flex flex-wrap gap-2">
                                <a
                                    href={`mailto:${contact.email}?subject=Re: Contact Form Submission`}
                                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Reply via Email
                                </a>
                                {contact.phone && (
                                    <a
                                        href={`tel:${contact.phone}`}
                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Call
                                    </a>
                                )}
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/admin-dashboard/contacts/"
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Back to List
                            </Link>
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                {loading ? 'Updating...' : 'Update Status'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default ContactEdit;

import React, { useState } from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const ContactsIndex = () => {
    const { props } = usePage();
    const { contacts } = props;
    const [searchTerm, setSearchTerm] = useState('');

    const filteredContacts = contacts.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.message.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleDelete = async (contactId) => {
        if (confirm('Are you sure you want to delete this contact submission?')) {
            try {
                const response = await fetch(`/admin-dashboard/contacts/${contactId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting contact submission: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting contact submission');
            }
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Contact Submissions</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage all contact form submissions.
                        </p>
                    </div>
                </div>

                {/* Search */}
                <div className="max-w-md">
                    <input
                        type="text"
                        placeholder="Search contact submissions..."
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {/* Contact Submissions Table */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul className="divide-y divide-gray-200">
                        {filteredContacts.length > 0 ? (
                            filteredContacts.map((contact) => (
                                <li key={contact.id}>
                                    <div className="px-4 py-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {contact.name}
                                                        </p>
                                                        <p className="text-sm text-gray-500">{contact.email}</p>
                                                        {contact.phone && (
                                                            <p className="text-sm text-gray-500">{contact.phone}</p>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                            contact.is_processed 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-yellow-100 text-yellow-800'
                                                        }`}>
                                                            {contact.is_processed ? 'Processed' : 'Pending'}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-900">
                                                        {contact.message.length > 200 
                                                            ? contact.message.substring(0, 200) + '...' 
                                                            : contact.message}
                                                    </p>
                                                </div>
                                                <div className="mt-2 text-xs text-gray-400">
                                                    Submitted: {new Date(contact.created_at).toLocaleString()}
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 ml-4">
                                                <Link
                                                    href={`/admin-dashboard/contacts/${contact.id}/edit/`}
                                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                                >
                                                    {contact.is_processed ? 'View' : 'Process'}
                                                </Link>
                                                <button
                                                    onClick={() => handleDelete(contact.id)}
                                                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))
                        ) : (
                            <li className="px-4 py-4 text-center text-gray-500">
                                {searchTerm ? 'No contact submissions found matching your search.' : 'No contact submissions found.'}
                            </li>
                        )}
                    </ul>
                </div>
            </div>
        </AdminLayout>
    );
};

export default ContactsIndex;

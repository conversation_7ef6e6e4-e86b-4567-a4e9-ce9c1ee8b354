import React, { useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const HireRequestEdit = () => {
    const { props } = usePage();
    const { hire_request, status_choices, talents } = props;
    
    const [formData, setFormData] = useState({
        status: hire_request.status || 'pending',
        talent_id: hire_request.talent?.id || ''
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const response = await fetch(`/admin-dashboard/hire-requests/${hire_request.id}/edit/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                router.visit(data.redirect);
            } else {
                setError(data.error || 'Failed to update hire request');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-xl font-semibold text-gray-900">Edit Hire Request</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Update hire request status and assignment.
                    </p>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    {/* Request Details (Read-only) */}
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Request Details</h3>
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Name</label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {hire_request.first_name} {hire_request.last_name}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Email</label>
                                <p className="mt-1 text-sm text-gray-900">{hire_request.email}</p>
                            </div>
                            {hire_request.phone && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                                    <p className="mt-1 text-sm text-gray-900">{hire_request.phone}</p>
                                </div>
                            )}
                            {hire_request.company && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Company</label>
                                    <p className="mt-1 text-sm text-gray-900">{hire_request.company}</p>
                                </div>
                            )}
                            <div className="sm:col-span-2">
                                <label className="block text-sm font-medium text-gray-700">Subject</label>
                                <p className="mt-1 text-sm text-gray-900">{hire_request.subject}</p>
                            </div>
                            <div className="sm:col-span-2">
                                <label className="block text-sm font-medium text-gray-700">Description</label>
                                <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                                    {hire_request.description}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Created</label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(hire_request.created_at).toLocaleString()}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(hire_request.updated_at).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Editable Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                                {error}
                            </div>
                        )}

                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                                    Status *
                                </label>
                                <select
                                    name="status"
                                    id="status"
                                    required
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.status}
                                    onChange={handleChange}
                                >
                                    {status_choices.map((choice) => (
                                        <option key={choice.value} value={choice.value}>
                                            {choice.label}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label htmlFor="talent_id" className="block text-sm font-medium text-gray-700">
                                    Assign to Talent
                                </label>
                                <select
                                    name="talent_id"
                                    id="talent_id"
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    value={formData.talent_id}
                                    onChange={handleChange}
                                >
                                    <option value="">-- Select Talent --</option>
                                    {talents.map((talent) => (
                                        <option key={talent.id} value={talent.id}>
                                            {talent.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/admin-dashboard/hire-requests/"
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                {loading ? 'Updating...' : 'Update Request'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default HireRequestEdit;

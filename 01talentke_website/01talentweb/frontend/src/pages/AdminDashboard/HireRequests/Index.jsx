import React, { useState } from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const HireRequestsIndex = () => {
    const { props } = usePage();
    const { hire_requests } = props;
    const [searchTerm, setSearchTerm] = useState('');

    const filteredRequests = hire_requests.filter(request =>
        request.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.subject.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleDelete = async (requestId) => {
        if (confirm('Are you sure you want to delete this hire request?')) {
            try {
                const response = await fetch(`/admin-dashboard/hire-requests/${requestId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting hire request: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting hire request');
            }
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'reviewed': return 'bg-blue-100 text-blue-800';
            case 'contacted': return 'bg-purple-100 text-purple-800';
            case 'completed': return 'bg-green-100 text-green-800';
            case 'rejected': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Hire Requests</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage all hire requests in the system.
                        </p>
                    </div>
                </div>

                {/* Search */}
                <div className="max-w-md">
                    <input
                        type="text"
                        placeholder="Search hire requests..."
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {/* Hire Requests Table */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul className="divide-y divide-gray-200">
                        {filteredRequests.length > 0 ? (
                            filteredRequests.map((request) => (
                                <li key={request.id}>
                                    <div className="px-4 py-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {request.first_name} {request.last_name}
                                                        </p>
                                                        <p className="text-sm text-gray-500">{request.email}</p>
                                                        {request.company && (
                                                            <p className="text-sm text-gray-500">{request.company}</p>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                                                            {request.status}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm font-medium text-gray-900">{request.subject}</p>
                                                    <p className="text-sm text-gray-500 mt-1">
                                                        {request.description.length > 150 
                                                            ? request.description.substring(0, 150) + '...' 
                                                            : request.description}
                                                    </p>
                                                </div>
                                                {request.talent && (
                                                    <div className="mt-2">
                                                        <p className="text-sm text-gray-500">
                                                            <span className="font-medium">Talent:</span> {request.talent.name}
                                                        </p>
                                                    </div>
                                                )}
                                                <div className="mt-2 text-xs text-gray-400">
                                                    Created: {new Date(request.created_at).toLocaleDateString()}
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 ml-4">
                                                <Link
                                                    href={`/admin-dashboard/hire-requests/${request.id}/edit/`}
                                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                                >
                                                    Edit
                                                </Link>
                                                <button
                                                    onClick={() => handleDelete(request.id)}
                                                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))
                        ) : (
                            <li className="px-4 py-4 text-center text-gray-500">
                                {searchTerm ? 'No hire requests found matching your search.' : 'No hire requests found.'}
                            </li>
                        )}
                    </ul>
                </div>
            </div>
        </AdminLayout>
    );
};

export default HireRequestsIndex;

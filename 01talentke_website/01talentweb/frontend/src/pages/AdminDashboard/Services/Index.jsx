import React, { useState } from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const ServicesIndex = () => {
    const { props } = usePage();
    const { services } = props;
    const [searchTerm, setSearchTerm] = useState('');

    const filteredServices = services.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleDelete = async (serviceId) => {
        if (confirm('Are you sure you want to delete this service?')) {
            try {
                const response = await fetch(`/admin-dashboard/services/${serviceId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting service: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting service');
            }
        }
    };

    const getCategoryColor = (category) => {
        switch (category) {
            case 'what_we_offer': return 'bg-blue-100 text-blue-800';
            case 'technical_expertise': return 'bg-green-100 text-green-800';
            case 'language': return 'bg-purple-100 text-purple-800';
            case 'framework': return 'bg-yellow-100 text-yellow-800';
            case 'specialization': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getCategoryLabel = (category) => {
        switch (category) {
            case 'what_we_offer': return 'What We Offer';
            case 'technical_expertise': return 'Technical Expertise';
            case 'language': return 'Language';
            case 'framework': return 'Framework';
            case 'specialization': return 'Specialization';
            default: return category;
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Services</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage all services in the system.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin-dashboard/services/create/"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                            Add Service
                        </Link>
                    </div>
                </div>

                {/* Search */}
                <div className="max-w-md">
                    <input
                        type="text"
                        placeholder="Search services..."
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                {/* Services Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {filteredServices.length > 0 ? (
                        filteredServices.map((service) => (
                            <div key={service.id} className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            {service.image ? (
                                                <img
                                                    className="h-10 w-10"
                                                    src={service.image}
                                                    alt={service.name}
                                                />
                                            ) : (
                                                <div className="h-10 w-10 bg-gray-300 rounded flex items-center justify-center">
                                                    <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                                    </svg>
                                                </div>
                                            )}
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    {service.title}
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {service.name}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                    <div className="mt-4">
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(service.category)}`}>
                                            {getCategoryLabel(service.category)}
                                        </span>
                                    </div>
                                    <div className="mt-4">
                                        <p className="text-sm text-gray-500">
                                            {service.description.length > 100 
                                                ? service.description.substring(0, 100) + '...' 
                                                : service.description}
                                        </p>
                                    </div>
                                    <div className="mt-4 text-xs text-gray-400">
                                        Created: {new Date(service.created_at).toLocaleDateString()}
                                    </div>
                                </div>
                                <div className="bg-gray-50 px-5 py-3">
                                    <div className="flex justify-end space-x-3">
                                        <Link
                                            href={`/admin-dashboard/services/${service.id}/edit/`}
                                            className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                        >
                                            Edit
                                        </Link>
                                        <button
                                            onClick={() => handleDelete(service.id)}
                                            className="text-red-600 hover:text-red-900 text-sm font-medium"
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="col-span-full text-center py-12">
                            <p className="text-gray-500">
                                {searchTerm ? 'No services found matching your search.' : 'No services found.'}
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
};

export default ServicesIndex;

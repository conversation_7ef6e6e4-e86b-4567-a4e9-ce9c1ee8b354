import React from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const ClientsIndex = () => {
    const { props } = usePage();
    const { clients } = props;

    const handleDelete = async (clientId) => {
        if (confirm('Are you sure you want to delete this client?')) {
            try {
                const response = await fetch(`/admin-dashboard/clients/${clientId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting client: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting client');
            }
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Clients</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage client logos and information.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin-dashboard/clients/create/"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                            Add Client
                        </Link>
                    </div>
                </div>

                {/* Clients Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {clients.length > 0 ? (
                        clients.map((client) => (
                            <div key={client.id} className="bg-white overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center justify-center h-32 bg-gray-50 rounded">
                                        {client.logo ? (
                                            <img
                                                src={client.logo}
                                                alt={client.name}
                                                className="max-h-24 max-w-full object-contain"
                                            />
                                        ) : (
                                            <div className="text-gray-400 text-center">
                                                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                                </svg>
                                                <p className="text-sm">No logo</p>
                                            </div>
                                        )}
                                    </div>
                                    <div className="mt-4">
                                        <h3 className="text-lg font-medium text-gray-900">{client.name}</h3>
                                        {client.website && (
                                            <a
                                                href={client.website}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-indigo-600 hover:text-indigo-900"
                                            >
                                                Visit Website
                                            </a>
                                        )}
                                    </div>
                                </div>
                                <div className="bg-gray-50 px-5 py-3">
                                    <div className="flex justify-end space-x-3">
                                        <Link
                                            href={`/admin-dashboard/clients/${client.id}/edit/`}
                                            className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                        >
                                            Edit
                                        </Link>
                                        <button
                                            onClick={() => handleDelete(client.id)}
                                            className="text-red-600 hover:text-red-900 text-sm font-medium"
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="col-span-full text-center py-12">
                            <p className="text-gray-500">No clients found.</p>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
};

export default ClientsIndex;

import React, { useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const BlogsEdit = () => {
    const { props } = usePage();
    const { blog } = props;
    
    const [formData, setFormData] = useState({
        title: blog.title || '',
        content: blog.content || '',
        link: blog.link || ''
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            const response = await fetch(`/admin-dashboard/blogs/${blog.id}/edit/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                router.visit(data.redirect);
            } else {
                setError(data.error || 'Failed to update blog post');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div>
                    <h1 className="text-xl font-semibold text-gray-900">Edit Blog Post</h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Update blog post information.
                    </p>
                </div>

                <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                                {error}
                            </div>
                        )}

                        <div>
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                                Title *
                            </label>
                            <input
                                type="text"
                                name="title"
                                id="title"
                                required
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                value={formData.title}
                                onChange={handleChange}
                            />
                        </div>

                        <div>
                            <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                                Content *
                            </label>
                            <textarea
                                name="content"
                                id="content"
                                rows={8}
                                required
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                value={formData.content}
                                onChange={handleChange}
                            />
                        </div>

                        <div>
                            <label htmlFor="link" className="block text-sm font-medium text-gray-700">
                                External Link (optional)
                            </label>
                            <input
                                type="url"
                                name="link"
                                id="link"
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                value={formData.link}
                                onChange={handleChange}
                            />
                            <p className="mt-1 text-sm text-gray-500">
                                If this blog post is published elsewhere, you can link to it here.
                            </p>
                        </div>

                        {blog.image && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Current Image</label>
                                <div className="mt-1">
                                    <img
                                        src={blog.image}
                                        alt={blog.title}
                                        className="h-32 w-auto object-cover rounded"
                                    />
                                </div>
                            </div>
                        )}

                        <div className="text-sm text-gray-500">
                            <p><strong>Last updated:</strong> {new Date(blog.updated_at).toLocaleString()}</p>
                        </div>

                        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-yellow-800">
                                        Image Upload
                                    </h3>
                                    <div className="mt-2 text-sm text-yellow-700">
                                        <p>Image upload functionality will be added in a future update. Currently, you can only edit the text content.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href="/admin-dashboard/blogs/"
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                disabled={loading}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                {loading ? 'Updating...' : 'Update Blog Post'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default BlogsEdit;

import React from 'react';
import { usePage, Link, router } from '@inertiajs/react';
import AdminLayout from '../components/AdminLayout';

const BlogsIndex = () => {
    const { props } = usePage();
    const { blogs } = props;

    const handleDelete = async (blogId) => {
        if (confirm('Are you sure you want to delete this blog post?')) {
            try {
                const response = await fetch(`/admin-dashboard/blogs/${blogId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content,
                    },
                });

                const data = await response.json();
                if (data.success) {
                    router.reload();
                } else {
                    alert('Error deleting blog post: ' + data.error);
                }
            } catch (error) {
                alert('Error deleting blog post');
            }
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-6">
                <div className="sm:flex sm:items-center">
                    <div className="sm:flex-auto">
                        <h1 className="text-xl font-semibold text-gray-900">Blog Posts</h1>
                        <p className="mt-2 text-sm text-gray-700">
                            Manage blog posts and articles.
                        </p>
                    </div>
                    <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <Link
                            href="/admin-dashboard/blogs/create/"
                            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
                        >
                            Add Blog Post
                        </Link>
                    </div>
                </div>

                {/* Blog Posts */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    {blogs.length > 0 ? (
                        <ul className="divide-y divide-gray-200">
                            {blogs.map((blog) => (
                                <li key={blog.id}>
                                    <div className="px-4 py-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex-1">
                                                <h3 className="text-lg font-medium text-gray-900">{blog.title}</h3>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-500">
                                                        {blog.content.length > 200 
                                                            ? blog.content.substring(0, 200) + '...' 
                                                            : blog.content}
                                                    </p>
                                                </div>
                                                {blog.link && (
                                                    <div className="mt-2">
                                                        <a
                                                            href={blog.link}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-sm text-indigo-600 hover:text-indigo-900"
                                                        >
                                                            View Article →
                                                        </a>
                                                    </div>
                                                )}
                                                {blog.image && (
                                                    <div className="mt-2">
                                                        <img 
                                                            src={blog.image} 
                                                            alt={blog.title} 
                                                            className="h-20 w-auto object-cover rounded"
                                                        />
                                                    </div>
                                                )}
                                                <div className="mt-2 text-xs text-gray-400">
                                                    Last updated: {new Date(blog.updated_at).toLocaleString()}
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 ml-4">
                                                <Link
                                                    href={`/admin-dashboard/blogs/${blog.id}/edit/`}
                                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                                >
                                                    Edit
                                                </Link>
                                                <button
                                                    onClick={() => handleDelete(blog.id)}
                                                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <div className="px-4 py-12 text-center">
                            <p className="text-gray-500">No blog posts found. Create one to get started.</p>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
};

export default BlogsIndex;

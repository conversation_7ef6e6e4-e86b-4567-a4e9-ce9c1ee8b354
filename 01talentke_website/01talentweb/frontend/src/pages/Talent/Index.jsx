import React, { useState } from 'react'; 
import { usePage } from '@inertiajs/react';
import Navbar from '../../components/Navbar.jsx';
import <PERSON> from '../../components/Hero.jsx';
import Button from '../../components/Button.jsx';

import TalentSection from './components/TalentSection.jsx';
import VetTalentSection from './components/How_we_vet.jsx';
import HireFormModal from '../../components/HireFormModal.jsx'; 

export default function Index() {
  const { props } = usePage();
  const talents = props.talents || [];

  // State to manage modal visibility and selected talent
  const [showHireModal, setShowHireModal] = useState(false); 
  const [selectedTalent, setSelectedTalent] = useState(null);

  // Function to open modal with or without a talent
  const handleHireClick = (talent = null) => {
    setSelectedTalent(talent);
    setShowHireModal(true);
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setShowHireModal(false);
    setSelectedTalent(null);
  };

  return (
    <>
      <Navbar />

      <Hero
        desktopBg="/static/images/talent_hero_desktop.jpg"
        mobileBg="/static/images/talent_hero_desktop.jpg"
        title={
          <>
            HIRE <span className="text-[--color-hero-text]">AI-READY TALENT</span> FROM US
          </>
        }
        description1="Our AI-ready tech talents are fully adaptable and equipped with hard and soft skills to work in fast-moving tech environments. We rigorously select the best tech talent from tens of thousands of applications across Kenya."
        description2="We ensure our talents have mastered languages such as JavaScript, Rust and GoLang and build an innovative, agile mind that can solve problems through creative application of tech."
        button={
          <Button
            onClick={() => handleHireClick()}
            className="
              w-full
              max-w-[377px] md:max-w-[377px] sm:max-w-[250px] h-[70px]
              whitespace-nowrap
              bg-[var(--color-primary-300)]
              hover:bg-[#284B81]
              transition-colors duration-300
              font-bold text-button-l
              py-6 px-20
              md:py-6 md:px-20
              sm:py-6 sm:px-8
              xs:py-6 xs:px-6 
            "
          >
            Hire Here
          </Button>
        }
      />

      {/* Hire Form Modal */}
      <HireFormModal
        isOpen={showHireModal}
        onClose={handleCloseModal}
        talent={selectedTalent}
      />

      {/* Talent list with hire button */}
      <TalentSection 
        talent={talents}
        onHire={handleHireClick}
      />

      <VetTalentSection />
    </>
  );
}

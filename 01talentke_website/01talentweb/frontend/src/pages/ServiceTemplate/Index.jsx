import React from 'react';
import { usePage } from '@inertiajs/react';
import Hero from '../../components/Hero.jsx';
import Button from '../../components/Button.jsx';
import ServiceVideo from './components/ServiceVideo.jsx';

export default function Index() {
  const { service, navbar_services } = usePage().props;

  // Fallbacks if service is not found
  const heroTitle = service?.title || "GET, TOP TALENT.";
  const heroDescription = service?.description || "No matter the service you need, we make hiring easy, fast, and reliable. From technical roles to creative projects, we've got your talent needs covered.";
  const templateIcon = service?.image || '/static/images/icons/default.svg';

  return (
    <>
      <Hero
        desktopBg="/static/images/service_hero_desktop.jpg"
        mobileBg="/static/images/service_hero_desktop.jpg"
        title={<>{heroTitle}</>}
        description1={heroDescription}
        button={
          <Button
            onClick={() => {}}
            className="
              hover:bg-[#284B81] 
              whitespace-nowrap 
              bg-[var(--color-primary-300)] 
              transition-colors 
              duration-300 
              !font-bold 
              text-button-l
              text-white
              w-full
              max-w-[377.24px]
              md:max-w-full
              h-[75.69px]
              px-[clamp(1rem,20vw,91.69px)]
              py-[clamp(1rem,5vh,22.92px)]
            "
          >
            Hire Here
          </Button>
        }
        template_chart_shapes="/static/images/shapes.svg"
        template_chart_web="/static/images/web.svg"
        template_icon={templateIcon}
        templateChartShapesSize="100%"
        templateChartWebSize="100%"
        templateIconSize="10%"
      />
      <ServiceVideo service={service} />
    </>
  );
}

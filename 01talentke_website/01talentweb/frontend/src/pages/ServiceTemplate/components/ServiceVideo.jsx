import React, { useEffect, useState } from 'react';
import { Container } from "../../../components/Layout.jsx";
import HireFormModal from '../../../components/HireFormModal.jsx';




export default function ServiceVideo({ service }) {
    const [showHireModal, setShowHireModal] = useState(false);
    
    const serviceDescription = service?.description || "No matter the service you need, we make hiring easy, fast, and reliable. From technical roles to creative projects, we've got your talent needs covered.";
  
  return (
    <section className="bg-blue-50 py-48 px-4 sm:px-6 lg:px-8 flex flex-col items-center">
      <Container>
      {/* Video wrapper: maintains 16:9 ratio */}
      <div className="w-full max-w-6xl relative" >
         <div className="aspect-w-16 aspect-h-9">
              <a 
                href="https://www.youtube.com/watch?v=EVtBa8HBjro&ab_channel=Zone01Kisumu" 
                target="_blank" 
                rel="noopener noreferrer"
                className="block relative group"
              >
                <img
                  src="../../../static/images/youtube.png"
                  alt="Meet Our Tech Tale"
                  className="w-full h-full object-cover rounded-lg"
                />
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg">
                  <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                    <svg 
                      className="w-5 h-5 text-white" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="2" 
                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                      />
                    </svg>
                  </div>
                </div>
              </a>
            </div>
      </div>

      {/* Content below */}
      <div className="mt-8 max-w-4xl text-center">
        <p className="text-gray-800 text-body-l leading-relaxed">
          {serviceDescription}
        </p>
      </div>

      {/* Button */}
      <div className="mt-6 flex justify-center">
        <a
          onClick={() => setShowHireModal(true)} 
          className="inline-block bg-blue-600 text-white font-semibold text-base sm:text-lg px-6 py-3 rounded-md shadow hover:bg-blue-700 transition cursor-pointer"
        >
          Hire Here
        </a>
      </div>
             {/* Hire Form Modal */}
               <HireFormModal
                  isOpen={showHireModal}
                  onClose={() => setShowHireModal(false)}
                />
      </Container>
    </section>
  );
}

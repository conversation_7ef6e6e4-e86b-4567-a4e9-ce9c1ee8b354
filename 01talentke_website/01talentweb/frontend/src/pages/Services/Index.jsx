import React, { useState, useEffect } from 'react';
import { usePage } from '@inertiajs/react';
import Hero from '../../components/Hero.jsx';
import Button from '../../components/Button.jsx';
import OurServices from './components/OurServices.jsx';
import TechnologiesSection from './components/Technologies.jsx';
import WhatMakesUsUnique from './components/WhatMakeUsUnique.jsx';
import { Link } from '@inertiajs/react';




export default function Index() {
  const { props } = usePage();
  const talents = props.talents || [];

    return (
      <>
            <Hero
                desktopBg="/static/images/service_hero_desktop.jpg"
                mobileBg="/static/images/service_hero_desktop.jpg"
                title={
                  <>
                    ACCESS <span className="text-[--color-hero-text] ">AI-READY TALENT,</span> TAILORED FOR YOUR BUSINESS.
                  </>
                }
                description1="We make hiring easy, fast, and reliable. That is what we are great at. From technical roles to creative projects, our AI-ready talent pool has you covered."
                button={
                 
                  <Link href="/contact" as="button" type="button">

                  <Button
                      variant="fill"
                      className="
                      hover:bg-[#284B81] 
                      whitespace-nowrap 
                      bg-[var(--color-primary-300)] 
                      transition-colors 
                      duration-300 
                      !font-bold 
                      text-button-l
                      text-white
                      w-full
                      max-w-[377.24px]
                      md:max-w-full
                      h-[75.69px]
                      px-[clamp(1rem,20vw,91.69px)]
                      py-[clamp(1rem,5vh,22.92px)]
                    "
                    >
                          Talk To An Expert
                        
                      
                  </Button></Link>
                }    
                chart="/static/images/chart.svg" 
                icons="/static/images/Apps.svg" 
                 chartSize="30%"  
                 iconsSize="25%" 
            />
            <OurServices/>
            <TechnologiesSection />
            <WhatMakesUsUnique />
               
      
       
       </>
     );
 }
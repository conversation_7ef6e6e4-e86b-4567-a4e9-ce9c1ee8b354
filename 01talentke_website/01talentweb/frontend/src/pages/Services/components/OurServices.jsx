import React, { useState, useEffect } from 'react';
import <PERSON><PERSON> from "../../../components/Button.jsx"; 
import { Container } from "../../../components/Layout.jsx";
import { Link } from '@inertiajs/react';


const OurServices = () => {
  const services = [
    {
      id: "../static/images/icons/01.svg",
      icon: "../static/images/icons/service_icon1.svg",
      title: "Software Development as a Service",
      bullets: [
        "We build customized software solutions for global clients. Our milestone-based payment models ensures that we only proceed to the next project only when you are happy with our solution. Join the growing number of satisfied global clients",
      ],
    },
    {
      id: "../static/images/icons/02.svg",
      icon: "../static/images/icons/service_icon2.svg", 
      title: "Staff Augmentation",
      bullets: [
        "Need more talent? Easily scale up teams with pre-vetted local developers. Need specialized talent to fit a specific need in your team? Pick and choose from a diverse range of AI–ready talent proficient in Full Stack, Front End, Back End, DevOps, UIUX, and Business Analysis",
      ],
    },
    {
      id: "../static/images/icons/03.svg",
      icon: "../static/images/icons/service_icon3.svg", 
      title: "Developers on Demand",
      bullets: [
        "Full-time, part-time or project-based work, we provide you immediate access to proficient developers for Flexible hiring for fintech, AI, blockchain, and cybersecurity",
      ],
    },
    {
      id: "../static/images/icons/04.svg",
      icon: "../static/images/icons/service_icon4.svg", 
      title: "Project Management",
      bullets: [
        "Our Agile-certified project managers have led delivery of multiple projects and ensured quality control of complex tasks through end-to-end project oversight and reporting. Our dedicated teams ensure continuity with KPI-driven performance from start to finish",
      ],
    },
    // {
    //   id: "../static/images/icons/05.svg",
    //   icon: "../static/images/icons/service_icon5.svg", 
    //   title: "Dedicated Teams",
    //   bullets: [
    //     "Long-term product development teams customized to client needs",
    //     "Team continuity with KPI-driven performance",
    //   ],
    // },
  ];

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
      <Container>
        <div className="max-w-9xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-h2 font-bold text-gray-900 mb-4">Our Services</h1>
            <p className="text-body-l text-gray-600 max-w-3xl mx-auto">
              No matter the service you need, we make hiring easy, fast, and reliable.

              From technical roles to creative projects, we've got your talent needs covered.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service) => (
              <div
                key={service.title}
                className="bg-[#EFF7FF] p-8 rounded-lg shadow-md border border-gray-100 transition-all duration-300 ease-in-out hover:scale-105 hover:-translate-y-1 hover:bg-gray-50 relative flex flex-col h-full" // Added flex-col and h-full
              >
                <div className="flex justify-between items-start">
                  <img 
                    src={service.id} 
                    alt="" 
                    className="w-8 h-8"
                  />
                  <img 
                    src={service.icon} 
                    alt={service.title} 
                    className="w-8 h-8 text-blue-600" 
                  />
                </div>
                
                <h3 className="text-h3 font-semibold text-gray-900 mt-10 mb-6">{service.title}</h3>
                
                <ul className="space-y-3 mb-6 flex-grow"> {/* Added flex-grow */}
                  {service.bullets.map((bullet, index) => (
                    <li key={index} className="flex items-start">
                      {/* <span className="text-blue-500 mr-2">•</span> */}
                      <span className="text-body-m text-gray-700">{bullet}</span>
                    </li>
                  ))}
                </ul>
                
                <div className="mt-auto flex justify-center"> 
                <Link href="/service_template" as="button" type="button">

                  <Button 
                    variant="outline"
                    className="flex items-center justify-center px-4 py-2 hover:bg-blue-500 hover:text-white rounded-md bg-transparent text-blue-500 hover:bg-blue-700 transition-colors duration-200">                  
                    Learn More
                  </Button>
                </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default OurServices;
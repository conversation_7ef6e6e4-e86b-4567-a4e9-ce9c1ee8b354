import React, { useState, useEffect } from 'react';
import { FaArrowRight } from 'react-icons/fa';
import { Container } from '../../../components/Layout.jsx';
import Button from '../../../components/Button';
import { Link } from '@inertiajs/react';

const WhatMakesUsUnique = () => {
  return (
    <section className="py-12">
      <Container>
        <div
          className="relative rounded-2xl overflow-hidden min-h-[280px] md:min-h-[340px] flex items-center bg-cover bg-center mb-12"
          style={{
            backgroundImage: "url('/static/images/whatsmorebg.jpg')",
          }}
        >
          {/* Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50" />

          {/* Content */}
          <div className="relative z-10 p-6 md:p-12 text-white w-[55%] ml-auto">
            <h2 className="text-h3 font-sans font-bold mb-4">
              What Makes Us Unique
            </h2>
            <p className="text-body-m font-sans mb-6">
            We are unlike any tech talent agency in the market. Our talents are multi-faceted, rooted in African contexts while at the same time providing homegrown solutions to the global tech market. Our talents are at the cutting-edge of modern technologies, ready to implement new technologies like AI, blockchain to solve your company's problems. Talk to us and let us embrace the future together.
            </p>
            <button className=" text-body-m font-sans inline-flex items-center gap-2 font-semibold group hover:underline transition">
              Improve workflow
              <FaArrowRight className="text-sm group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
        <div className='w-full flex justify-center'>
        <Link href="/contact" as="button" type="button">

            <Button
                variant='filled'
                fontFamily="var(--font-sans)"
                style={{ minWidthn : 'fit-content' }}
                className={'text-button-l font-bold px-6 md:px-10 py-2'}
            >
                Talk to an Expert
            </Button>
        </Link>
        </div>
      </Container>
    </section>
  );
};

export default WhatMakesUsUnique;

import React, { useState } from 'react';
import { Container } from '../../../components/Layout.jsx';
import { FaCode } from 'react-icons/fa';
import Button from '../../../components/Button';
import { Link, usePage } from '@inertiajs/react';

const TechnologiesSection = () => {
  const [visibleItems, setVisibleItems] = useState({});
  const { navbar_services } = usePage().props;

  const loadMoreItems = (categoryIndex) => {
    setVisibleItems(prev => ({
      ...prev,
      [categoryIndex]: (prev[categoryIndex] || 6) + 6
    }));
  };

  const getServiceUrl = (tech, category) => {
    const params = new URLSearchParams();
    params.set(category, tech.name);
    return `/service_template?${params.toString()}`;
  };

  // Build categories dynamically from navbar_services data
  const categories = [];
  
  if (navbar_services?.languages?.length > 0) {
    categories.push({
      title: 'Programming Languages',
      category: 'language',
      items: navbar_services.languages.map(service => ({
        label: service.name,
        name: service.name,
        icon: service.image ? (
          <img src={service.image} alt={service.name} className="w-6 h-6" />
        ) : (
          <FaCode style={{ color: '#6e5494' }} />
        )
      }))
    });
  }

  if (navbar_services?.frameworks?.length > 0) {
    categories.push({
      title: 'Frameworks and Libraries',
      category: 'framework',
      items: navbar_services.frameworks.map(service => ({
        label: service.name,
        name: service.name,
        icon: service.image ? (
          <img src={service.image} alt={service.name} className="w-6 h-6" />
        ) : (
          <FaCode style={{ color: '#6e5494' }} />
        )
      }))
    });
  }

  if (navbar_services?.specializations?.length > 0) {
    categories.push({
      title: 'Specializations',
      category: 'specialization',
      items: navbar_services.specializations.map(service => ({
        label: service.name,
        name: service.name,
        icon: service.image ? (
          <img src={service.image} alt={service.name} className="w-6 h-6" />
        ) : (
          <FaCode style={{ color: '#6e5494' }} />
        )
      }))
    });
  }

  return (
    <section className="bg-[--color-primary-0] pb-12">
      <Container className="text-center">
        <h2 className="text-h2 font-semibold font-sans text-[--color-primary-800] mb-2">Technologies</h2>
        <p className="text-body-m font-sans mb-12">
          Showcasing talent skill sets across:
        </p>

        {categories.map((category, index) => (
          <div key={index} className="mb-12">
            <h3 className="text-body-xl font-bold text-[--color-primary-800] mb-6">{category.title}</h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 md:gap-20 justify-items-center">
              {category.items.slice(0, visibleItems[index] || 6).map((tech, idx) => (

                <Link
                  key={idx}
                  href={getServiceUrl(tech, category.category)}
                  className="flex items-center justify-center gap-3 mx-8 py-8 border border-gray-200 bg-white rounded-2xl shadow-sm w-full text-gray-700 text-body-xl font-sans transition-all duration-300 ease-in-out transform hover:scale-105 hover:-translate-y-1 hover:bg-gray-50 hover:shadow-md hover:border-[--color-primary-500] hover:text-[--color-primary-500] cursor-pointer"
                >
                  <span className="text-body-2xl">{tech.icon}</span>
                  {tech.label}
                </Link>
              ))}
            </div>

            {category.items.length > (visibleItems[index] || 6) && (
              <div className="flex justify-center mt-10">
                <Button
                  variant='outline'
                  onClick={() => loadMoreItems(index)}
                  className="text-button-cardm px-10 py-2 text-[--color-primary-600] font-bold"
                >
                  More
                </Button>
              </div>
            )}
          </div>
        ))}
      </Container>
    </section>
  );
};

export default TechnologiesSection;

import React from 'react';
import { usePage } from '@inertiajs/react';
import ContactForm from './components/ContactForm.jsx';
import Location from './components/Location.jsx';

import Button from '../../components/Button.jsx';




export default function Index() {
  const { props } = usePage();

    return (
    <>
        <ContactForm
            desktopDeco="/static/images/desktop-contact-decorations.svg"
            mobileDeco="/static/images/mobile-contact-decorations.svg"

        />
        <Location />
    </>
     );
 }
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { Container } from '../../../components/Layout.jsx';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default marker icons (required for React)
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

const Location = () => {
  // Coordinates for Zorvoff Pictures in Khanun, Kenya (example coordinates)
  const position = [-0.06559, 34.77444]; // [latitude, longitude]

  return (
    <section className="py-0 md:py-12 mb-20 mt-0 md:mt-10 bg-white">
      <Container>
      <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* <h2 className="text-3xl font-bold text-center text-gray-800 mb-8 hidden lg:block">Our Location</h2> */}

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Map container */}
          <div className="lg:w-1/2 rounded-lg overflow-hidden shadow-md h-[250px] lg:h-[400px] relative z-0">
            <h2 className="text-2xl font-bold text-center text-gray-800 mb-4 md:hidden">Our Location</h2>
            <MapContainer
              center={position}
              zoom={15}
              scrollWheelZoom={false}
              style={{ height: "100%", width: "100%" }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              <Marker position={position}>
                <Popup>
                  <b>Zone01 Kisumu</b><br />
                  Lake-Basin Mall<br />
                  Kisumu-Vihiga Road<br/>
                  Kisumu, Kenya
                </Popup>
              </Marker>
            </MapContainer>
          </div>

          {/* Text content */}
          <div className="lg:w-1/2">
                  <h2 className="text-h3 font-bold  text-gray-800 mb-8 xs:hidden sm:block">Our Location</h2>

            <h3 className="text-xl font-semibold text-gray-700 mb-6">Visit Our Offices</h3>

            {/* Desktop version */}
            <div className="hidden lg:block">
              <p className="text-gray-600">Zone01 Kisumu</p>
              <p className="text-gray-600">Lake-Basin Mall, Kisumu-Vihiga Road</p>
              <p className="text-gray-600">Kisumu, Kenya</p>
            </div>

            {/* Mobile version */}
            <div className="lg:hidden">
              <p className="text-gray-600">Zone01 Kisumu</p>
              <p className="text-gray-600">Lake-Basin Mall, Kisumu-Vihiga Road</p>
              <p className="text-gray-600">Kisumu, Kenya</p>

            </div>
          </div>
        </div>
      </div>
      </Container>
    </section>
  );
};

export default Location;
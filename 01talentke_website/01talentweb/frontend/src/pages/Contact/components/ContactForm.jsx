import React from 'react';
import { Container } from '../../../components/Layout.jsx';


export default function ContactForm({ desktopDeco, mobileDeco }) {
  return (
    <section className="min-h-[90vh] flex items-center justify-center bg-white md:p-4 md:mt-20">
      <div className="relative overflow-hidden w-full">

        {/* Mobile Layout */}
        <div className="md:hidden relative h-[500px]">
          {/* Blue Background Decoration - Half width, positioned on the right */}
          <div className="absolute top-0 right-0 w-1/2 h-full bg-blue-600 rounded-l-3xl z-0 flex justify-end items-center">
            {mobileDeco && (
              <img
                src={mobileDeco}
                alt="Decorative circles"
                className="h-full object-contain "
              />
            )}
          </div>

          {/* Mobile Form Content - Positioned at the very left edge */}
          <div className="absolute top-8 left-0 w-[85%] bg-white rounded-r-2xl shadow-lg p-4 z-10">
            <h2 className="text-h2 font-bold">
              Get in <span className="text-blue-600">Touch</span>
            </h2>
            <p className="mt-1 text-body-s  font-bold">You can reach us any time</p>
            <form className="mt-4 space-y-3">
              <input
                type="text"
                placeholder="Name *"
                required
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              />
              <input
                type="email"
                placeholder="Email"
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              />
              <input
                type="tel"
                placeholder="Phone number *"
                required
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              />
              <textarea
                placeholder="Your message"
                rows={3}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
              />
              <button
                type="submit"
                className="w-full py-2 text-sm bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition"
              >
                Send
              </button>
            </form>
          </div>
        </div>

        {/* Desktop Layout */}
        <Container>
        <div className="hidden md:grid md:grid-cols-[3fr_1fr]  rounded-3xl shadow-lg border border-gray-200 rounded">
          {/* Desktop Form Section */}
          <div className="px-16 py-8 ">
            <h2 className="text-h2 font-bold">
              Get in <span className="text-blue-600">Touch</span>
            </h2>
            <p className="mt-2 text-gray-600 font-bold">You can reach us any time</p>
            <form className="mt-8 space-y-4">
              <input
                type="text"
                placeholder="Name *"
                required
                className="w-full px-4 py-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="email"
                placeholder="Email"
                className="w-full px-4 py-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="tel"
                placeholder="Phone number *"
                required
                className="w-full px-4 py-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <textarea
                placeholder="Your message"
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="submit"
                className="w-full py-3 bg-blue-600 text-body-l text-white font-semibold rounded hover:bg-blue-700 transition"
              >
                Send
              </button>
            </form>
          </div>

          {/* Desktop Decoration Section */}
          <div className="relative bg-blue-600 flex justify-end rounded-r-3xl">
            {desktopDeco && (
              <img
                src={desktopDeco}
                alt="Decorative circles"
                className="object-contain "
              />
            )}
          </div>
        </div>
        </Container>
      </div>
    </section>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar hiding utility */
@layer utilities {
  .scrollbar-hide {
    /* Hide scrollbar for Chrome, Safari and Opera */
    -webkit-scrollbar: none;
    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

@layer base {
  :root {
    /* Typography */
    --font-sans: 'Montserrat', system-ui, -apple-system, sans-serif;
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    
    --text-h0: clamp(28px, 3.5vw, 38px);
    --text-h1: clamp(40px, 5vw, 64px);
    --text-h2: clamp(22px, 4vw, 60px);
    --text-h3: clamp(16px, 1.5vw, 38px);
    
    /* Body Text - using clamp for fluid scaling */
    --text-body-xxl: clamp(28px, 3vw, 36px);
    --text-body-xl: clamp(24px, 2.5vw, 28px);
    --text-body-l: clamp(16px, 2vw, 22px);
    --text-body-m: clamp(14px, 1.5vw, 20px);
    --text-body-s: clamp(12px, 1.25vw, 18px);
    --text-body-xs: clamp(8px, 1.125vw, 12px);

    /*buttons texts*/
    --text-button-s: clamp(7px, 1.5vw, 18px);
    --text-button-l: clamp(14px, 1.5vw, 24px);
    --text-button-cards: clamp(9px, 1.125vw, 14px);
    --text-button-cardm: clamp(10px, 1.25vw, 24px);

    
    /* Line Heights - adjusted for better readability */
    --leading-h1: 1.3;
    --leading-h2: 1.15;
    --leading-h3: 1.05;
    --leading-body-l: 1.5;
    --leading-body-m: 1.4;
    --leading-normal: 1.5;
    
    /* Spacing Scale with fluid values */
    --space-0: 0;
    --space-1: clamp(0.5rem, 1vw, 0.75rem);
    --space-2: clamp(1rem, 2vw, 1.5rem);
    --space-3: clamp(1.5rem, 3vw, 2rem);
    --space-4: clamp(2rem, 4vw, 2.5rem);
    --space-5: clamp(2.5rem, 5vw, 3rem);
    --space-6: clamp(3rem, 6vw, 3.5rem);
    --space-8: clamp(3rem, 8vw, 6rem);
    
    /* Color Palette - Primary Blues */
    --color-primary-0: #FFFFFF;
    --color-primary-50: #EEF7FF;
    --color-primary-100: #3385FF;
    --color-primary-200: #1F78FF;
    --color-primary-300: #0063F9;
    --color-primary-400: #005AED;
    --color-primary-500: #0052CC;
    --color-primary-600: #2B5290;
    --color-primary-700: #284B81;
    --color-primary-800: #000000;
    --color-primary-900: #0F172A;
    --color-primary-button-600: #0063F9;
    --color-hero-text: #005AE0;
    --color-newsletter-border: #0A142F;
    
    /* Color Palette - Informational */
    --color-error: #EA191D;
    --color-warning: #FF9500;
    --color-success: #02DADB;
    --color-info: #0559DA;
    
    /* Semantic Colors */
    --color-text-heading: #142C5E;
    --color-text: #0f172a;
    --color-text-muted: #64748b;
    --color-bg: #ffffff;
    --color-bg-muted: #f8fafc;
    --color-border: #e2e8f0;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 82 204 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 82 204 / 0.1), 0 1px 2px -1px rgb(0 82 204 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 82 204 / 0.1), 0 2px 4px -2px rgb(0 82 204 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
  }
}

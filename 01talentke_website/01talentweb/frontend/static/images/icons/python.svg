<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_2115_7428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="1" y="1" width="29" height="29">
<path d="M1.28125 1.38281H29.066V29.1987H1.28125V1.38281Z" fill="white"/>
</mask>
<g mask="url(#mask0_2115_7428)">
<mask id="mask1_2115_7428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="1" y="1" width="29" height="29">
<path d="M19.8745 1.37891C10.3734 1.37891 10.9667 5.49932 10.9667 5.49932L10.9772 9.76818H20.043V11.0498H7.37625C7.37625 11.0498 1.29688 10.3603 1.29688 19.9472C1.29677 29.5349 6.60313 29.1949 6.60313 29.1949H9.76979V24.7456C9.76979 24.7456 9.59906 19.4387 14.9914 19.4387H23.9835C23.9835 19.4387 29.0348 19.5208 29.0348 14.5559V6.34682C29.0348 6.34682 29.8026 1.37891 19.8745 1.37891ZM14.8749 4.24943C15.7768 4.24943 16.5055 4.97859 16.5055 5.88068C16.5055 6.78286 15.7768 7.51193 14.8749 7.51193C13.9727 7.51193 13.2438 6.78286 13.2438 5.88068C13.2438 4.97859 13.9727 4.24943 14.8749 4.24943Z" fill="white"/>
</mask>
<g mask="url(#mask1_2115_7428)">
<path d="M1.29688 1.38281V29.5348H29.8027V1.38281H1.29688Z" fill="url(#paint0_linear_2115_7428)"/>
</g>
</g>
<mask id="mask2_2115_7428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="12" y="10" width="28" height="29">
<path d="M12.0625 10.7734H39.8225V38.6241H12.0625V10.7734Z" fill="white"/>
</mask>
<g mask="url(#mask2_2115_7428)">
<mask id="mask3_2115_7428" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="12" y="10" width="28" height="29">
<path d="M21.2459 38.6146C30.7461 38.6146 30.1526 34.4935 30.1526 34.4935L30.1424 30.2243H21.0759V28.9434H33.7429C33.7429 28.9434 39.8228 29.6322 39.8228 20.0459C39.8228 10.4584 34.5167 10.7984 34.5167 10.7984H31.3499V15.2471C31.3499 15.2471 31.5199 20.5545 26.1286 20.5545H17.1355C17.1355 20.5545 12.0841 20.4724 12.0841 25.4377V33.6464C12.0841 33.6464 11.317 38.6146 21.2459 38.6146ZM26.2444 35.7436C25.3432 35.7436 24.6134 35.0137 24.6134 34.1124C24.6134 33.2096 25.3432 32.4812 26.2444 32.4812C27.1471 32.4812 27.8754 33.2096 27.8754 34.1124C27.8754 35.0137 27.1471 35.7436 26.2444 35.7436Z" fill="white"/>
</mask>
<g mask="url(#mask3_2115_7428)">
<path d="M11.3203 10.457V38.6133H39.8261V10.457H11.3203Z" fill="url(#paint1_linear_2115_7428)"/>
</g>
</g>
<defs>
<linearGradient id="paint0_linear_2115_7428" x1="1.41265" y1="1.26583" x2="29.8858" y2="29.4509" gradientUnits="userSpaceOnUse">
<stop stop-color="#387EB8"/>
<stop offset="0.125" stop-color="#387EB8"/>
<stop offset="0.140625" stop-color="#387EB8"/>
<stop offset="0.15625" stop-color="#387DB7"/>
<stop offset="0.171875" stop-color="#387DB6"/>
<stop offset="0.1875" stop-color="#387CB5"/>
<stop offset="0.203125" stop-color="#387CB4"/>
<stop offset="0.21875" stop-color="#387BB3"/>
<stop offset="0.234375" stop-color="#387BB2"/>
<stop offset="0.25" stop-color="#387AB1"/>
<stop offset="0.265625" stop-color="#387AB1"/>
<stop offset="0.28125" stop-color="#3879B0"/>
<stop offset="0.296875" stop-color="#3879AF"/>
<stop offset="0.3125" stop-color="#3878AE"/>
<stop offset="0.328125" stop-color="#3778AD"/>
<stop offset="0.34375" stop-color="#3777AC"/>
<stop offset="0.359375" stop-color="#3777AB"/>
<stop offset="0.375" stop-color="#3776AB"/>
<stop offset="0.390625" stop-color="#3776AA"/>
<stop offset="0.40625" stop-color="#3775A9"/>
<stop offset="0.421875" stop-color="#3775A8"/>
<stop offset="0.4375" stop-color="#3774A7"/>
<stop offset="0.453125" stop-color="#3774A6"/>
<stop offset="0.46875" stop-color="#3773A5"/>
<stop offset="0.484375" stop-color="#3773A4"/>
<stop offset="0.494335" stop-color="#3772A4"/>
<stop offset="0.5" stop-color="#3772A3"/>
<stop offset="0.505665" stop-color="#3772A3"/>
<stop offset="0.515625" stop-color="#3771A2"/>
<stop offset="0.53125" stop-color="#3771A2"/>
<stop offset="0.546875" stop-color="#3771A1"/>
<stop offset="0.5625" stop-color="#3770A0"/>
<stop offset="0.578125" stop-color="#37709F"/>
<stop offset="0.59375" stop-color="#376F9E"/>
<stop offset="0.609375" stop-color="#376F9D"/>
<stop offset="0.625" stop-color="#376E9D"/>
<stop offset="0.640625" stop-color="#366E9C"/>
<stop offset="0.65625" stop-color="#366D9B"/>
<stop offset="0.671875" stop-color="#366D9A"/>
<stop offset="0.6875" stop-color="#366C99"/>
<stop offset="0.703125" stop-color="#366C98"/>
<stop offset="0.71875" stop-color="#366B97"/>
<stop offset="0.734375" stop-color="#366B97"/>
<stop offset="0.75" stop-color="#366A96"/>
<stop offset="0.765625" stop-color="#366A95"/>
<stop offset="0.78125" stop-color="#366994"/>
<stop offset="0.8125" stop-color="#366994"/>
<stop offset="0.875" stop-color="#366994"/>
<stop offset="1" stop-color="#366994"/>
</linearGradient>
<linearGradient id="paint1_linear_2115_7428" x1="11.1677" y1="10.6184" x2="40.2786" y2="38.1346" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE052"/>
<stop offset="0.125" stop-color="#FFE052"/>
<stop offset="0.1875" stop-color="#FFE052"/>
<stop offset="0.21875" stop-color="#FFE052"/>
<stop offset="0.234375" stop-color="#FFDF51"/>
<stop offset="0.25" stop-color="#FFDF51"/>
<stop offset="0.265625" stop-color="#FFDE50"/>
<stop offset="0.28125" stop-color="#FFDD4F"/>
<stop offset="0.296875" stop-color="#FFDD4E"/>
<stop offset="0.3125" stop-color="#FFDC4E"/>
<stop offset="0.328125" stop-color="#FFDB4D"/>
<stop offset="0.34375" stop-color="#FFDB4C"/>
<stop offset="0.359375" stop-color="#FFDA4B"/>
<stop offset="0.375" stop-color="#FFD94B"/>
<stop offset="0.390625" stop-color="#FFD94A"/>
<stop offset="0.40625" stop-color="#FFD849"/>
<stop offset="0.421875" stop-color="#FFD748"/>
<stop offset="0.4375" stop-color="#FFD748"/>
<stop offset="0.453125" stop-color="#FFD647"/>
<stop offset="0.46875" stop-color="#FFD546"/>
<stop offset="0.482837" stop-color="#FFD545"/>
<stop offset="0.484375" stop-color="#FFD445"/>
<stop offset="0.5" stop-color="#FFD445"/>
<stop offset="0.515625" stop-color="#FFD444"/>
<stop offset="0.517163" stop-color="#FFD343"/>
<stop offset="0.53125" stop-color="#FFD343"/>
<stop offset="0.546875" stop-color="#FFD242"/>
<stop offset="0.5625" stop-color="#FFD242"/>
<stop offset="0.578125" stop-color="#FFD141"/>
<stop offset="0.59375" stop-color="#FFD040"/>
<stop offset="0.609375" stop-color="#FFD03F"/>
<stop offset="0.625" stop-color="#FFCF3F"/>
<stop offset="0.640625" stop-color="#FFCE3E"/>
<stop offset="0.65625" stop-color="#FFCE3D"/>
<stop offset="0.671875" stop-color="#FFCD3C"/>
<stop offset="0.6875" stop-color="#FFCC3C"/>
<stop offset="0.703125" stop-color="#FFCC3B"/>
<stop offset="0.71875" stop-color="#FFCB3A"/>
<stop offset="0.734375" stop-color="#FFCA39"/>
<stop offset="0.75" stop-color="#FFCA39"/>
<stop offset="0.765625" stop-color="#FFC938"/>
<stop offset="0.78125" stop-color="#FFC837"/>
<stop offset="0.796875" stop-color="#FFC836"/>
<stop offset="0.8125" stop-color="#FFC735"/>
<stop offset="0.828125" stop-color="#FFC635"/>
<stop offset="0.84375" stop-color="#FFC634"/>
<stop offset="0.859375" stop-color="#FFC533"/>
<stop offset="0.875" stop-color="#FFC432"/>
<stop offset="0.890625" stop-color="#FFC432"/>
<stop offset="0.90625" stop-color="#FFC331"/>
<stop offset="0.9375" stop-color="#FFC331"/>
<stop offset="1" stop-color="#FFC331"/>
</linearGradient>
</defs>
</svg>

{"name": "dirt", "version": "1.0.0", "description": "", "scripts": {"vite-build": "vite build", "vite-dev": "vite", "tailwind-dev": "tailwindcss -i ./static/css/main.css -o ./static/dist/css/app.css --watch", "dirt-dev": "concurrently \"npm run tailwind-dev\" \"npm run vite-dev\" ", "test": "jest"}, "author": "", "license": "ISC", "devDependencies": {"@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@inertiajs/react": "^1.0.8", "@tailwindcss/aspect-ratio": "^0.4.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.4.0", "babel-jest": "^30.0.4", "concurrently": "^8.2.2", "fluid-tailwind": "^1.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.5.3", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}, "dependencies": {"leaflet": "^1.9.4", "lucide-react": "^0.511.0", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^7.6.3"}}
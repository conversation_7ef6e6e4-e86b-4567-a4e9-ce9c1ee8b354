import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import FeaturedProfiles from '../src/pages/Home/components/FeaturedProfiles';

// Mock talents data
const talents = [
  { id: 1, name: 'Talent 1', role: 'Role 1', image: 'img1.jpg' },
  { id: 2, name: 'Talent 2', role: 'Role 2', image: 'img2.jpg' },
  { id: 3, name: 'Talent 3', role: 'Role 3', image: 'img3.jpg' },
  { id: 4, name: 'Talent 4', role: 'Role 4', image: 'img4.jpg' },
];

describe('FeaturedProfiles', () => {
  // test('navigates to /talent when View All Talents button is clicked', () => {
  //   render(
  //     <MemoryRouter>
  //       <FeaturedProfiles talents={talents} />
  //     </MemoryRouter>
  //   );
  //   const viewAllButton = screen.getByRole('button', { name: /view all talents/i });
  //   expect(viewAllButton).toBeInTheDocument();
  //   // Simulate click (navigation assertion depends on implementation)
  //   fireEvent.click(viewAllButton);
  //   // You may want to check for navigation side effect here if possible
  // });

  test('handleNext and handlePrev functions update the carousel', () => {
    render(
      <MemoryRouter>
        <FeaturedProfiles talents={talents} />
      </MemoryRouter>
    );
    // Interact with the first visible talent card
    const talentCards = screen.getAllByText(/Talent \d/);
    expect(talentCards.length).toBeGreaterThan(0);
    // Click next
    const nextButton = screen.getByRole('button', { name: /next/i });
    fireEvent.click(nextButton);
    // Click prev
    const prevButton = screen.getByRole('button', { name: /prev/i });
    fireEvent.click(prevButton);
    // No assertion on visible card due to JSDOM limitations
  });

  test('handleTalentClick opens the modal for the first talent', async () => {
    render(
      <MemoryRouter>
        <FeaturedProfiles talents={talents} />
      </MemoryRouter>
    );
    // Interact with the first element from getAllByText
    const talentCards = screen.getAllByText(/Talent \d/);
    expect(talentCards.length).toBeGreaterThan(0);
    fireEvent.click(talentCards[0]);
  });
}); 
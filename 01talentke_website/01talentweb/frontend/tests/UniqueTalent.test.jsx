import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UniqueTalentSection from '../src/pages/Home/components/UniqueTalent';

// Mock developers array since it's referenced in the component
const developers = [
  { id: 1, name: 'Dev 1' },
  { id: 2, name: 'Dev 2' },
  { id: 3, name: 'Dev 3' },
  { id: 4, name: 'Dev 4' },
];

describe('UniqueTalentSection', () => {
  beforeAll(() => {
    // Attach developers to global scope if needed by the component
    global.developers = developers;
  });
  afterAll(() => {
    delete global.developers;
  });

  // test('Hire Developers button disables on click and shows loading, then re-enables', async () => {
  //   render(<UniqueTalentSection />);
  //   const hireButton = screen.getByRole('button', { name: /hire developers/i });
  //   expect(hireButton).toBeEnabled();
  //   await userEvent.click(hireButton);
  //   // Button should be disabled and show 'Submitting...'
  //   expect(hireButton).toBeDisabled();
  //   expect(hireButton).toHaveTextContent(/submitting/i);
  //   // Wait for the button to be enabled again (waits up to 2s)
  //   await waitFor(() => {
  //     expect(screen.getByRole('button', { name: /hire developers/i })).toBeEnabled();
  //   }, { timeout: 2000 });
  // });

  // test('Hire Developers button navigates to /hire after submit', async () => {
  //   const navMock = jest.fn();
  //   render(<UniqueTalentSection navigate={navMock} />);
  //   const hireButton = screen.getByRole('button', { name: /hire developers/i });
  //   await userEvent.click(hireButton);

  //   await waitFor(() => {
  //     expect(navMock).toHaveBeenCalled();
  //   }, { timeout: 2000 });
  // });
}); 
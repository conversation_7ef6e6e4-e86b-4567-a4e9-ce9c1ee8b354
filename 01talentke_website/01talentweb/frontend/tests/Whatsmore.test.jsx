// import React from 'react';
// import { render, screen } from '@testing-library/react';
// import userEvent from '@testing-library/user-event';
// import WhatsMoreSection from '../src/pages/Home/components/Whatsmore';

// describe('WhatsMoreSection', () => {
//   test('Talk to Us button triggers navigation when clicked', async () => {
//     const navMock = jest.fn();
//     render(<WhatsMoreSection navigate={navMock} />);
//     const talkButton = screen.getByRole('button', { name: /talk to us/i });
//     await userEvent.click(talkButton);
//     expect(navMock).toHaveBeenCalled();
//   });
// });
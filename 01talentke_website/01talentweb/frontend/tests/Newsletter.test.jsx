import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import NewsletterSection from '../src/pages/Home/components/Newsletter';

describe('NewsletterSection', () => {
  test('disables the button after submit to prevent DoS', async () => {
    render(<NewsletterSection />);
    const emailInput = screen.getByPlaceholderText(/enter your email/i);
    const subscribeButton = screen.getByRole('button', { name: /subscribe now/i });

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.click(subscribeButton);

    // But<PERSON> should be disabled and show 'Submitting...'
    expect(subscribeButton).toBeDisabled();
    expect(subscribeButton).toHaveTextContent(/submitting/i);

    // Wait for the button to be enabled again (waits up to 2s)
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /subscribe now/i })).toBeEnabled();
    }, { timeout: 2000 });
  });
}); 
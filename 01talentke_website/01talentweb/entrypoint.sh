#!/bin/bash
set -e

echo "🚀 Starting Django application..."

# Load environment variables from .env.prod if it exists (for local Docker)
if [ -f ".env.prod" ]; then
    echo "📋 Loading environment variables from .env.prod..."
    export $(grep -v '^#' .env.prod | xargs)
fi

# Simple database connection check for Fly.io
if [ -n "$DATABASE_URL" ]; then
    echo "📋 Using Fly.io DATABASE_URL"
elif [ -n "$DB_HOST" ]; then
    echo "📋 Using individual database environment variables"
    echo "DB_HOST: ${DB_HOST}"
    echo "DB_PORT: ${DB_PORT:-5432}"

    # Simple connection check without extensive debugging
    echo "⏳ Waiting for database connection..."
    counter=0
    max_attempts=30

    while ! nc -z ${DB_HOST} ${DB_PORT:-5432} 2>/dev/null; do
        counter=$((counter + 1))
        if [ $counter -gt $max_attempts ]; then
            echo "❌ Database connection timeout after ${max_attempts} seconds"
            exit 1
        fi
        if [ $((counter % 5)) -eq 0 ]; then
            echo "Still waiting for database... (attempt $counter/$max_attempts)"
        fi
        sleep 1
    done
    echo "✅ Database connection established!"
else
    echo "⚠️ No database configuration found, continuing..."
fi

# Note: On Fly.io, migrations and static files are handled by release_command in fly.toml
# Only collect static files and run migrations if not on Fly.io
if [ -z "$FLY_APP_NAME" ]; then
    echo "📚 Collecting static files..."
    python manage.py collectstatic --noinput --clear

    echo "🔄 Running database migrations..."
    python manage.py migrate

    # Create superuser if needed (optional, only for local development)
    echo "👤 Checking for superuser..."
    python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(is_superuser=True).exists():
    print('Creating superuser...')
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
" || echo "⚠️ Superuser creation skipped"
else
    echo "🚀 Running on Fly.io - migrations and static files handled by release_command"
fi

# Start Gunicorn
echo "🚀 Starting Gunicorn server..."
exec gunicorn --workers 2 \
    --bind 0.0.0.0:${PORT:-8000} \
    --timeout 120 \
    --access-logfile - \
    --error-logfile - \
    --log-level info \
    zone01web.wsgi:application
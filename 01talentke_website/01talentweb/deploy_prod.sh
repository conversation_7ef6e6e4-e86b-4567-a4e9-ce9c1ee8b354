#!/bin/bash

echo "🚀 Starting production deployment process..."

# Load environment variables
if [ -f "backend/.env.prod" ]; then
    echo "Loading production environment variables..."
    source backend/.env.prod
else
    echo "⚠️ Production environment file not found. Please create backend/.env.prod"
    exit 1
fi

# Build frontend
echo "📦 Building frontend assets..."
cd frontend
if ! npm run vite-build; then
    echo "❌ Frontend build failed!"
    exit 1
fi
cd ..

# Activate virtual environment and install requirements
echo "🔧 Setting up Python environment..."
cd backend
python3 -m venv venv
source venv/bin/activate
if ! pip install -r requirements.txt; then
    echo "❌ Failed to install Python requirements!"
    exit 1
fi

# Collect static files
echo "📚 Collecting static files..."
python manage.py collectstatic --noinput --clear

# Run database migrations
echo "🔄 Running database migrations..."
python manage.py migrate

# Start Gunicorn
echo "🚀 Starting Gunicorn server..."
gunicorn --workers 3 \
         --bind 127.0.0.1:8000 \
         --access-logfile - \
         --error-logfile - \
         --log-level info \
         --capture-output \
         zone01web.wsgi:application

"""
Views for the Services app.

This module provides endpoints for listing and managing service data.

Key Views:
- services: Returns a list of all services for the frontend.

Additional endpoints can be added here to support CRUD operations or custom queries for the Service model.
"""

from inertia import inertia, render
from services.models import Service

def serialize_service(service, request=None):
    return {
        'id': str(service.id),
        'name': service.name,
        'title': service.title,
        'description': service.description,
        'category': service.category,
        'image': request.build_absolute_uri(service.image.url) if (request and service.image) else (service.image.url if service.image else ''),
        'created_at': service.created_at,
    }

def get_navbar_services(request):
    """Helper function to get services grouped by category for navbar"""
    services = Service.objects.all()
    
    navbar_services = {
        'what_we_offer': [],
        'languages': [],
        'frameworks': [],
        'specializations': []
    }
    
    for service in services:
        service_data = {
            'name': service.name,
            'title': service.title,
            'image': request.build_absolute_uri(service.image.url) if (request and service.image) else (service.image.url if service.image else '')
        }
        
        if service.category == 'what_we_offer':
            navbar_services['what_we_offer'].append(service_data)
        elif service.category == 'language':
            navbar_services['languages'].append(service_data)
        elif service.category == 'framework':
            navbar_services['frameworks'].append(service_data)
        elif service.category == 'specialization':
            navbar_services['specializations'].append(service_data)
    
    return navbar_services

@inertia('Services/Index')
def services(request):
    services = Service.objects.all()
    service_list = [serialize_service(service, request) for service in services]
    
    # Group services by category for navbar
    navbar_services = get_navbar_services(request)
    
    return {
        'services': service_list,
        'navbar_services': navbar_services
    }

def service_template(request):
    service_name = request.GET.get('service')
    language = request.GET.get('language')
    framework = request.GET.get('framework')
    specialization = request.GET.get('specialization')
    
    service = None
    
    if service_name:
        service = Service.objects.filter(name__iexact=service_name).first()
    elif language:
        service = Service.objects.filter(name__iexact=language, category='language').first()
    elif framework:
        service = Service.objects.filter(name__iexact=framework, category='framework').first()
    elif specialization:
        service = Service.objects.filter(name__iexact=specialization, category='specialization').first()
    
    return render(request, 'ServiceTemplate/Index', {
        'service': serialize_service(service, request) if service else None,
        'navbar_services': get_navbar_services(request)
    })

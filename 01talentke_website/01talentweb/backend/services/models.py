"""
Model definitions for the Services app.
Defines the Service model, which represents a service offered and its related metadata.
"""

from django.db import models
from django.core.exceptions import ValidationError
import uuid

def validate_svg(file):
    if not file.name.lower().endswith('.svg'):
        raise ValidationError("Only SVG files are allowed.")
    
    # Additional validation to check if it's actually an SVG file
    try:
        file.seek(0)
        content = file.read(1024).decode('utf-8', errors='ignore')
        file.seek(0)
        if not content.strip().startswith('<svg') and '<svg' not in content:
            raise ValidationError("File is not a valid SVG.")
    except Exception:
        raise ValidationError("Invalid SVG file.")

class Service(models.Model):
    CATEGORY_CHOICES = [
        ('what_we_offer', 'What We Offer'),
        ('technical_expertise', 'Technical Expertise'),
        ('language', 'Language'),
        ('framework', 'Framework'),
        ('specialization', 'Specialization'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255, default='Untitled')
    name = models.CharField(max_length=255)
    description = models.TextField()
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        default='what_we_offer',
        help_text="Category of the service"
    )
    image = models.FileField(
        upload_to='services/',
        null=True,
        blank=True,
        validators=[validate_svg],
        help_text="Upload SVG icon for the service"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'services'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

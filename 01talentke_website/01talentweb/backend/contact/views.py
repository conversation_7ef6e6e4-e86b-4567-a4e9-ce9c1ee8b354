"""
Views for the Contact app.

Handles contact page rendering and form submissions with database storage.
"""

from django.http import JsonResponse
from inertia import inertia
from .models import ContactSubmission
from services.views import get_navbar_services
import json
import uuid

@inertia('Contact/Index')
def contact(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Create and save new contact submission
            submission = ContactSubmission(
                id=uuid.uuid4(),
                name=data.get('name'),
                email=data.get('email'),
                phone=data.get('phone'),
                message=data.get('message')
            )
            submission.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Thank you for your message! We will get back to you soon.',
                'submission_id': str(submission.id)
            })
        
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
    
    # GET request - return initial props
    return {
        'title': 'Contact Us',
        'description': 'Get in touch with our team',
        'initialFormValues': {
            'name': '',
            'email': '',
            'phone': '',
            'message': ''
        },
        'meta': {
            'total_submissions': ContactSubmission.objects.count()
        },
        'navbar_services': get_navbar_services(request)
    }
"""
URL configuration for project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))

"""
from django.contrib import admin
from django.urls import path, include
from .views import *
from .health import health_check, readiness_check, liveness_check
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('', index, name='home'),
    path('admin/', admin.site.urls),
    path('talent/', include('talent.urls')),
    path('services/', include('services.urls')),
    path('contact/', include('contact.urls')),
    path('info/', include('info.urls')),
    path('service_template/', include('service_template.urls')),
    path('hire/', include('hiring.urls')),
    # Health check endpoints
    path('health/', health_check, name='health'),
    path('healthz/', health_check, name='healthz'),  # Common Kubernetes endpoint
    path('ready/', readiness_check, name='readiness'),
    path('live/', liveness_check, name='liveness'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
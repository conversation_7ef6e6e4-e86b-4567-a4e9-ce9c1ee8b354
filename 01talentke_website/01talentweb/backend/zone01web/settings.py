"""
Django settings for zone01web zone01web.

Generated by 'django-admin startzone01web' using Django 4.2.2.

For more information on this file, see
https://docs.djangozone01web.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangozone01web.com/en/4.2/ref/settings/
"""

from pathlib import Path
import re
from dotenv import load_dotenv
import os
import dj_database_url

load_dotenv()

# Build paths inside the zone01web like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Check if running in Docker or Fly.io
IN_DOCKER = os.getenv('IN_DOCKER', 'False').lower() == 'true'
ON_FLY = os.getenv('FLY_APP_NAME') is not None

if IN_DOCKER or ON_FLY:
    # In Docker or Fly.io, the templates are in the working directory
    BASE_DIR = Path('/app')

APP_NAME = "zone01web"

# Quick-start development settings - unsuitable for production
# See https://docs.djangozone01web.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Add Fly.io hosts
if ON_FLY:
    ALLOWED_HOSTS.extend([
        f"{os.getenv('FLY_APP_NAME')}.fly.dev",
        f"{os.getenv('FLY_APP_NAME')}.flycast",
    ])

# Security settings for production
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
# Fly.io handles SSL termination, so don't force redirect
SECURE_SSL_REDIRECT = not DEBUG and not ON_FLY
SESSION_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_SECURE = not DEBUG
SECURE_HSTS_SECONDS = 31536000 if not DEBUG and not ON_FLY else 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = not DEBUG and not ON_FLY
SECURE_HSTS_PRELOAD = not DEBUG and not ON_FLY

# CSRF trusted origins for production
CSRF_TRUSTED_ORIGINS = []
if ON_FLY:
    CSRF_TRUSTED_ORIGINS.extend([
        f"https://{os.getenv('FLY_APP_NAME')}.fly.dev",
        f"https://{os.getenv('FLY_APP_NAME')}.flycast",
    ])
else:
    CSRF_TRUSTED_ORIGINS.extend([
        'http://localhost:8000',
        'http://127.0.0.1:8000',
    ])

# Application definition

INSTALLED_APPS = [
    "whitenoise.runserver_nostatic",
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_vite',
    'rest_framework',
    'hiring',
    'inertia',
    'talent',
    'services',
    'contact',
    'info',
    'service_template'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    "whitenoise.middleware.WhiteNoiseMiddleware",
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    #django inertia
    'inertia.middleware.InertiaMiddleware',
    # Inertia Share
    "zone01web.middleware.inertia_share",
]

STORAGES = {
    # ...
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
    },
}

ROOT_URLCONF = 'zone01web.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates'
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'zone01web.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Use DATABASE_URL if available (Fly.io), otherwise use individual env vars
if os.getenv('DATABASE_URL'):
    DATABASES = {
        'default': dj_database_url.parse(os.getenv('DATABASE_URL'))
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DB_NAME'),
            'USER': os.getenv('DB_USER'),
            'PASSWORD': os.getenv('DB_PASSWORD'),
            'HOST': os.getenv('DB_HOST'),
            'PORT': os.getenv('DB_PORT'),
        }
    }

# Password validation
# https://docs.djangozone01web.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangozone01web.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / "staticfiles"

# Media files (Uploaded files)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Include DJANGO_VITE_ASSETS_PATH into STATICFILES_DIRS to be copied inside
# when run command python manage.py collectstatic
STATICFILES_DIRS = [
    BASE_DIR / 'frontend' / 'static',
    BASE_DIR / 'frontend' / 'dist'
]

# Default primary key field type
# https://docs.djangozone01web.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# django inertia vite

INERTIA_LAYOUT = 'base.html'

# We need this for django form posting
CSRF_HEADER_NAME = 'HTTP_X_XSRF_TOKEN'
CSRF_COOKIE_NAME = 'XSRF-TOKEN'

# Where ViteJS assets are built.
DJANGO_VITE_ASSETS_PATH = BASE_DIR / 'frontend' / 'dist'

# Where the manifest file is
DJANGO_VITE_MANIFEST_PATH = DJANGO_VITE_ASSETS_PATH / '.vite' / 'manifest.json'

DJANGO_VITE_STATIC_URL_PREFIX = ''
# If we should use HMR or not.
DJANGO_VITE_DEV_MODE = False  # Changed from DEBUG to False

# These settings are only used when DJANGO_VITE_DEV_MODE is True
DJANGO_VITE_DEV_SERVER_HOST = '127.0.0.1'
DJANGO_VITE_DEV_SERVER_PORT = 3000

# we need this to get around cors issues
DJANGO_VITE_DEV_SERVER_HOST = '127.0.0.1'

DJANGO_VITE_PORT = 3000

def immutable_file_test(path, url):
    # Match filename with 12 hex digits before the extension
    # e.g. app.db8f2edc0c8a.js
    # path parameter is required by WhiteNoise but not used in this implementation
    return re.match(r'^.+\.[0-9a-f]{12}\..+$', url)

WHITENOISE_IMMUTABLE_FILE_TEST = immutable_file_test
"""
Health check views for monitoring application status.
"""
from django.http import JsonResponse
from django.db import connection
from django.conf import settings
import os


def health_check(request):
    """
    Basic health check endpoint for load balancers and monitoring.
    Returns 200 if the application is healthy.
    """
    try:
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'environment': 'production' if not settings.DEBUG else 'development'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=503)


def readiness_check(request):
    """
    Readiness check for Kubernetes/container orchestration.
    """
    return JsonResponse({'status': 'ready'})


def liveness_check(request):
    """
    Liveness check for container orchestration.
    """
    return JsonResponse({'status': 'alive'})
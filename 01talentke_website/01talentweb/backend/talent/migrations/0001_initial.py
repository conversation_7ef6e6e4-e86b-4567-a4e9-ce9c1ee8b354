# Generated by Django 4.2.2 on 2025-06-26 10:08

import django.contrib.postgres.indexes
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Talent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('profile', models.JSONField(default=dict)),
                ('image', models.ImageField(blank=True, null=True, upload_to='talents/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('featured_order', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'talents',
                'indexes': [models.Index(fields=['email'], name='talents_email_eab52a_idx'), django.contrib.postgres.indexes.GinIndex(fields=['profile'], name='profile_gin_idx'), models.Index(fields=['featured_order'], name='talents_feature_bd9e99_idx')],
            },
        ),
    ]

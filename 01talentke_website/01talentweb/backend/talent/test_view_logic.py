"""
Tests for talent view logic without Inertia complications.

This module tests the core business logic of talent views by testing
the data processing and filtering logic directly.
"""

from django.test import TestCase, RequestFactory
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch
from talent.models import Talent
import json


class TalentViewLogicTest(TestCase):
    """Test the core logic of talent views."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        
        # Create test talents
        self.talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Alice Backend',
            profile={
                'role': 'Backend Developer',
                'bio': 'Experienced Python developer',
                'location': 'Nairobi',
                'skills': ['Python', 'Django', 'PostgreSQL'],
                'average_rating': 4.8,
                'is_available': True
            }
        )
        
        self.talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='Bob Frontend',
            profile={
                'role': 'Frontend Developer',
                'bio': 'React specialist',
                'location': 'Mombasa',
                'skills': ['JavaScript', 'React', 'CSS'],
                'average_rating': 4.6,
                'is_available': False
            }
        )
        
        self.talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Carol Fullstack',
            profile={
                'role': 'Full Stack Developer',
                'bio': 'Full stack with DevOps',
                'location': 'Nairobi',
                'skills': ['Python', 'React', 'Docker'],
                'average_rating': 4.9
            }
        )

    def get_talents_list_data(self, request):
        """Extract the core logic from talents_list view."""
        from django.db.models import Q
        
        search_query = request.GET.get('q', '').strip()
        role_filter = request.GET.get('role', '').strip()
        talents = Talent.objects.all()
        
        # Apply role filter if specified
        if role_filter:
            talents = talents.filter(profile__role=role_filter)
        
        if search_query:
            # Search in name field
            name_query = Q(name__icontains=search_query)
            
            # Search in JSON fields
            profile_query = (
                Q(profile__bio__icontains=search_query) |
                Q(profile__role__icontains=search_query) |
                Q(profile__location__icontains=search_query) |
                Q(profile__skills__icontains=search_query)
            )
            
            # Combine queries
            talents = talents.filter(name_query | profile_query)
            
        # Get unique roles for filter dropdown
        all_roles = Talent.objects.exclude(profile__role='').values_list('profile__role', flat=True).distinct()

        talents_list = []
        for talent in talents:
            skills = talent.profile.get('skills', [])
            talent_dict = {
                'id': talent.id,
                'email': talent.email,
                'name': talent.name,
                'image': request.build_absolute_uri(talent.image.url) if talent.image else None,
                'skills': skills,
                'average_rating': talent.profile.get('average_rating', 4.5),
                'profile': {
                    **talent.profile,
                    'is_available': talent.profile.get('is_available', True),
                },
                'created_at': talent.created_at,
            }
            talents_list.append(talent_dict)

        return {
            'talents': talents_list,
            'filters': {
                'search': search_query,
                'role': role_filter,
            },
            'available_roles': list(all_roles),
        }

    def get_talent_page_data(self, request):
        """Extract the core logic from talent_page view."""
        talents = list(Talent.objects.all().order_by('id'))
        formatted_talents = []
        
        for talent in talents:
            skills = talent.profile.get('skills', [])
            formatted_talents.append({
                'id': str(talent.id),
                'name': talent.name,
                'image': request.build_absolute_uri(talent.image.url) if talent.image else None,
                'skills': skills,
                'average_rating': talent.profile.get('average_rating', 4.5),
                'profile': talent.profile,
            })
        
        return {
            'talents': formatted_talents,
        }

    def test_talents_list_no_filters(self):
        """Test talents_list without any filters."""
        request = self.factory.get('/talents/')
        data = self.get_talents_list_data(request)
        
        # Should return all talents
        self.assertEqual(len(data['talents']), 3)
        self.assertEqual(data['filters']['search'], '')
        self.assertEqual(data['filters']['role'], '')

    def test_talents_list_role_filter(self):
        """Test talents_list with role filtering."""
        request = self.factory.get('/talents/?role=Backend Developer')
        data = self.get_talents_list_data(request)
        
        # Should return only backend developers
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Alice Backend')
        self.assertEqual(data['filters']['role'], 'Backend Developer')

    def test_talents_list_search_by_name(self):
        """Test talents_list with name search."""
        request = self.factory.get('/talents/?q=Alice')
        data = self.get_talents_list_data(request)
        
        # Should return only Alice
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Alice Backend')
        self.assertEqual(data['filters']['search'], 'Alice')

    def test_talents_list_search_by_role(self):
        """Test talents_list with role search."""
        request = self.factory.get('/talents/?q=Frontend')
        data = self.get_talents_list_data(request)
        
        # Should return Frontend developers
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Bob Frontend')

    def test_talents_list_search_by_bio(self):
        """Test talents_list with bio search."""
        request = self.factory.get('/talents/?q=Python developer')
        data = self.get_talents_list_data(request)
        
        # Should return Alice (has "Python developer" in bio)
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Alice Backend')

    def test_talents_list_search_by_location(self):
        """Test talents_list with location search."""
        request = self.factory.get('/talents/?q=Nairobi')
        data = self.get_talents_list_data(request)
        
        # Should return talents in Nairobi (Alice and Carol)
        self.assertEqual(len(data['talents']), 2)
        talent_names = [t['name'] for t in data['talents']]
        self.assertIn('Alice Backend', talent_names)
        self.assertIn('Carol Fullstack', talent_names)

    def test_talents_list_search_by_skills(self):
        """Test talents_list with skills search."""
        request = self.factory.get('/talents/?q=React')
        data = self.get_talents_list_data(request)
        
        # Should return talents with React skills (Bob and Carol)
        self.assertEqual(len(data['talents']), 2)
        talent_names = [t['name'] for t in data['talents']]
        self.assertIn('Bob Frontend', talent_names)
        self.assertIn('Carol Fullstack', talent_names)

    def test_talents_list_combined_filters(self):
        """Test talents_list with both role filter and search."""
        request = self.factory.get('/talents/?role=Backend Developer&q=Python')
        data = self.get_talents_list_data(request)
        
        # Should return Alice (Backend Developer with Python)
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Alice Backend')
        self.assertEqual(data['filters']['role'], 'Backend Developer')
        self.assertEqual(data['filters']['search'], 'Python')

    def test_talents_list_no_results(self):
        """Test talents_list with search that returns no results."""
        request = self.factory.get('/talents/?q=NonexistentSkill')
        data = self.get_talents_list_data(request)
        
        # Should return empty list
        self.assertEqual(len(data['talents']), 0)
        self.assertEqual(data['filters']['search'], 'NonexistentSkill')

    def test_talents_list_available_roles(self):
        """Test that available_roles contains all unique roles."""
        request = self.factory.get('/talents/')
        data = self.get_talents_list_data(request)
        
        # Check available roles
        available_roles = data['available_roles']
        expected_roles = ['Backend Developer', 'Frontend Developer', 'Full Stack Developer']
        
        for role in expected_roles:
            self.assertIn(role, available_roles)

    def test_talents_list_data_formatting(self):
        """Test that talents_list formats data correctly."""
        request = self.factory.get('/talents/')
        data = self.get_talents_list_data(request)
        
        # Find Alice's data
        alice_data = next(t for t in data['talents'] if t['name'] == 'Alice Backend')
        
        # Check data structure and values
        expected_keys = ['id', 'email', 'name', 'image', 'skills', 'average_rating', 'profile', 'created_at']
        for key in expected_keys:
            self.assertIn(key, alice_data)
        
        # Check specific values
        self.assertEqual(alice_data['email'], '<EMAIL>')
        self.assertEqual(alice_data['skills'], ['Python', 'Django', 'PostgreSQL'])
        self.assertEqual(alice_data['average_rating'], 4.8)
        self.assertTrue(alice_data['profile']['is_available'])

    def test_talents_list_default_availability(self):
        """Test that is_available defaults to True when not set."""
        request = self.factory.get('/talents/')
        data = self.get_talents_list_data(request)
        
        # Find Carol's data (doesn't have is_available set)
        carol_data = next(t for t in data['talents'] if t['name'] == 'Carol Fullstack')
        
        # Should default to True
        self.assertTrue(carol_data['profile']['is_available'])

    def test_talent_page_data_structure(self):
        """Test that talent_page returns correctly formatted data."""
        request = self.factory.get('/talent/')
        data = self.get_talent_page_data(request)
        
        # Check that response contains expected keys
        self.assertIn('talents', data)
        
        talents = data['talents']
        self.assertEqual(len(talents), 3)  # All 3 test talents
        
        # Check first talent structure
        talent = talents[0]
        expected_keys = ['id', 'name', 'image', 'skills', 'average_rating', 'profile']
        for key in expected_keys:
            self.assertIn(key, talent)

    def test_talent_page_data_formatting(self):
        """Test that talent data is correctly formatted."""
        request = self.factory.get('/talent/')
        data = self.get_talent_page_data(request)
        talents = data['talents']
        
        # Find our test talent
        alice_talent = next(t for t in talents if t['name'] == 'Alice Backend')
        
        # Check data formatting
        self.assertEqual(alice_talent['name'], 'Alice Backend')
        self.assertEqual(alice_talent['skills'], ['Python', 'Django', 'PostgreSQL'])
        self.assertEqual(alice_talent['average_rating'], 4.8)
        self.assertEqual(alice_talent['profile']['role'], 'Backend Developer')
        self.assertIsInstance(alice_talent['id'], str)  # UUID converted to string

    def test_talent_page_ordering(self):
        """Test that talents are ordered by ID."""
        request = self.factory.get('/talent/')
        data = self.get_talent_page_data(request)
        talents = data['talents']

        # Check that talents are ordered by ID (actual creation order)
        talent_names = [t['name'] for t in talents]
        # The actual order depends on UUID generation, so just check we have all talents
        self.assertEqual(len(talent_names), 3)
        self.assertIn('Alice Backend', talent_names)
        self.assertIn('Bob Frontend', talent_names)
        self.assertIn('Carol Fullstack', talent_names)

    def test_case_insensitive_search(self):
        """Test that search is case insensitive."""
        request = self.factory.get('/talents/?q=ALICE')  # Uppercase
        data = self.get_talents_list_data(request)
        
        # Should still find Alice
        self.assertEqual(len(data['talents']), 1)
        self.assertEqual(data['talents'][0]['name'], 'Alice Backend')

    def test_whitespace_search_handling(self):
        """Test talents_list with whitespace-only search query."""
        request = self.factory.get('/talents/?q=   ')  # Only whitespace
        data = self.get_talents_list_data(request)
        
        # Should return all talents (whitespace stripped)
        self.assertEqual(len(data['talents']), 3)
        self.assertEqual(data['filters']['search'], '')

    def test_empty_role_filter(self):
        """Test talents_list with empty role filter."""
        request = self.factory.get('/talents/?role=')
        data = self.get_talents_list_data(request)
        
        # Should return all talents
        self.assertEqual(len(data['talents']), 3)
        self.assertEqual(data['filters']['role'], '')

"""
Comprehensive tests for talent views.

This module tests all view functionality including:
- talents_list view with search and filtering
- talent_page view with data formatting
- URL routing and response handling
- Edge cases and error conditions
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch, MagicMock
from talent.models import Talent
from services.models import Service
import json
import uuid


class TalentViewsTestCase(TestCase):
    """Base test case for talent views with common setup."""

    def setUp(self):
        """Set up test data for view tests."""
        self.client = Client()
        
        # Create test talents with various profiles
        self.talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Alice Backend',
            profile={
                'role': 'Backend Developer',
                'bio': 'Experienced Python developer',
                'location': 'Nairobi',
                'skills': ['Python', 'Django', 'PostgreSQL'],
                'average_rating': 4.8,
                'is_available': True
            }
        )
        
        self.talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='<PERSON>end',
            profile={
                'role': 'Frontend Developer',
                'bio': 'React specialist with great UI skills',
                'location': 'Mombasa',
                'skills': ['JavaScript', 'React', 'CSS'],
                'average_rating': 4.6,
                'is_available': False
            }
        )
        
        self.talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Carol Fullstack',
            profile={
                'role': 'Full Stack Developer',
                'bio': 'Full stack developer with DevOps experience',
                'location': 'Nairobi',
                'skills': ['Python', 'React', 'Docker', 'AWS'],
                'average_rating': 4.9
                # Note: is_available not set (should default to True)
            }
        )
        
        # Create talent with image
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
        test_image = SimpleUploadedFile(
            name='test_image.png',
            content=image_content,
            content_type='image/png'
        )
        
        self.talent_with_image = Talent.objects.create(
            email='<EMAIL>',
            name='Dave Designer',
            image=test_image,
            profile={
                'role': 'UI/UX Designer',
                'bio': 'Creative designer with modern aesthetics',
                'location': 'Kisumu',
                'skills': ['Figma', 'Adobe XD', 'Sketch'],
                'average_rating': 4.7,
                'is_available': True
            }
        )
        
        # Create talent with minimal profile
        self.talent_minimal = Talent.objects.create(
            email='<EMAIL>',
            name='Eve Minimal',
            profile={}  # Empty profile to test defaults
        )

    def tearDown(self):
        """Clean up uploaded files after tests."""
        if self.talent_with_image.image:
            try:
                self.talent_with_image.image.delete()
            except:
                pass


class TalentPageViewTest(TalentViewsTestCase):
    """Test the talent_page view functionality."""

    def get_talent_page_data(self, request):
        """Helper method to get talent_page data without Inertia wrapper."""
        from talent.views import get_navbar_services

        talents = list(Talent.objects.all().order_by('id'))
        formatted_talents = []

        for talent in talents:
            skills = talent.profile.get('skills', [])
            formatted_talents.append({
                'id': str(talent.id),
                'name': talent.name,
                'image': request.build_absolute_uri(talent.image.url) if talent.image else None,
                'skills': skills,
                'average_rating': talent.profile.get('average_rating', 4.5),
                'profile': talent.profile,
            })

        return {
            'talents': formatted_talents,
            'navbar_services': get_navbar_services(request)
        }

    @patch('talent.views.get_navbar_services')
    def test_talent_page_basic_functionality(self, mock_navbar_services):
        """Test basic talent_page view functionality."""
        mock_navbar_services.return_value = {'test': 'services'}

        response = self.client.get(reverse('talent:talent'))

        self.assertEqual(response.status_code, 200)
        mock_navbar_services.assert_called_once()

        # Check that it's an Inertia response
        self.assertEqual(response['Content-Type'], 'text/html; charset=utf-8')

    @patch('talent.views.get_navbar_services')
    def test_talent_page_data_structure(self, mock_navbar_services):
        """Test that talent_page returns correctly formatted data."""
        mock_navbar_services.return_value = {'test': 'services'}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talent/')

        # Test using our helper method
        data = self.get_talent_page_data(request)

        # Check that response contains expected keys
        self.assertIn('talents', data)
        self.assertIn('navbar_services', data)

        talents = data['talents']
        self.assertEqual(len(talents), 5)  # All 5 test talents

        # Check first talent structure
        talent = talents[0]
        expected_keys = ['id', 'name', 'image', 'skills', 'average_rating', 'profile']
        for key in expected_keys:
            self.assertIn(key, talent)

    @patch('talent.views.get_navbar_services')
    def test_talent_page_data_formatting(self, mock_navbar_services):
        """Test that talent data is correctly formatted."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talent/')

        data = self.get_talent_page_data(request)
        talents = data['talents']

        # Find our test talent
        alice_talent = next(t for t in talents if t['name'] == 'Alice Backend')

        # Check data formatting
        self.assertEqual(alice_talent['name'], 'Alice Backend')
        self.assertEqual(alice_talent['skills'], ['Python', 'Django', 'PostgreSQL'])
        self.assertEqual(alice_talent['average_rating'], 4.8)
        self.assertEqual(alice_talent['profile']['role'], 'Backend Developer')
        self.assertIsInstance(alice_talent['id'], str)  # UUID converted to string

    @patch('talent.views.get_navbar_services')
    def test_talent_page_image_handling(self, mock_navbar_services):
        """Test image URL handling in talent_page."""
        mock_navbar_services.return_value = {}
        
        response = self.client.get(reverse('talent:talent'))
        talents = response.context['talents']
        
        # Find talent with image
        dave_talent = next(t for t in talents if t['name'] == 'Dave Designer')
        self.assertIsNotNone(dave_talent['image'])
        self.assertTrue(dave_talent['image'].startswith('http'))
        
        # Find talent without image
        alice_talent = next(t for t in talents if t['name'] == 'Alice Backend')
        self.assertIsNone(alice_talent['image'])

    @patch('talent.views.get_navbar_services')
    def test_talent_page_default_values(self, mock_navbar_services):
        """Test default values for missing profile fields."""
        mock_navbar_services.return_value = {}
        
        response = self.client.get(reverse('talent:talent'))
        talents = response.context['talents']
        
        # Find talent with minimal profile
        eve_talent = next(t for t in talents if t['name'] == 'Eve Minimal')
        
        # Check defaults
        self.assertEqual(eve_talent['skills'], [])  # Empty list for missing skills
        self.assertEqual(eve_talent['average_rating'], 4.5)  # Default rating
        self.assertEqual(eve_talent['profile'], {})  # Empty profile

    @patch('talent.views.get_navbar_services')
    def test_talent_page_ordering(self, mock_navbar_services):
        """Test that talents are ordered by ID."""
        mock_navbar_services.return_value = {}
        
        response = self.client.get(reverse('talent:talent'))
        talents = response.context['talents']
        
        # Check that talents are ordered by ID (creation order in our case)
        talent_names = [t['name'] for t in talents]
        expected_order = ['Alice Backend', 'Bob Frontend', 'Carol Fullstack', 'Dave Designer', 'Eve Minimal']
        self.assertEqual(talent_names, expected_order)


class TalentsListViewTest(TalentViewsTestCase):
    """Test the talents_list view functionality."""

    def get_talents_list_url(self):
        """Helper to get the talents_list URL (not in urlpatterns but used internally)."""
        # Since talents_list is not in URLs, we'll test it directly
        from talent.views import talents_list
        return talents_list

    @patch('talent.views.get_navbar_services')
    def test_talents_list_basic_functionality(self, mock_navbar_services):
        """Test basic talents_list view functionality."""
        mock_navbar_services.return_value = {'test': 'services'}
        
        # Create a mock request
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')
        
        from talent.views import talents_list
        response_data = talents_list(request)
        
        # Check response structure
        self.assertIn('talents', response_data)
        self.assertIn('filters', response_data)
        self.assertIn('available_roles', response_data)
        self.assertIn('navbar_services', response_data)
        
        mock_navbar_services.assert_called_once_with(request)

    @patch('talent.views.get_navbar_services')
    def test_talents_list_no_filters(self, mock_navbar_services):
        """Test talents_list without any filters."""
        mock_navbar_services.return_value = {}
        
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')
        
        from talent.views import talents_list
        response_data = talents_list(request)
        
        # Should return all talents
        self.assertEqual(len(response_data['talents']), 5)
        
        # Check filters
        self.assertEqual(response_data['filters']['search'], '')
        self.assertEqual(response_data['filters']['role'], '')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_role_filter(self, mock_navbar_services):
        """Test talents_list with role filtering."""
        mock_navbar_services.return_value = {}
        
        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?role=Backend Developer')
        
        from talent.views import talents_list
        response_data = talents_list(request)
        
        # Should return only backend developers
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Alice Backend')
        
        # Check filters
        self.assertEqual(response_data['filters']['role'], 'Backend Developer')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_search_by_name(self, mock_navbar_services):
        """Test talents_list with name search."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=Alice')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return only Alice
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Alice Backend')

        # Check filters
        self.assertEqual(response_data['filters']['search'], 'Alice')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_search_by_role(self, mock_navbar_services):
        """Test talents_list with role search in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=Frontend')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return Frontend developers
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Bob Frontend')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_search_by_bio(self, mock_navbar_services):
        """Test talents_list with bio search in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=Python developer')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return Alice (has "Python developer" in bio)
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Alice Backend')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_search_by_location(self, mock_navbar_services):
        """Test talents_list with location search in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=Nairobi')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return talents in Nairobi (Alice and Carol)
        self.assertEqual(len(response_data['talents']), 2)
        talent_names = [t['name'] for t in response_data['talents']]
        self.assertIn('Alice Backend', talent_names)
        self.assertIn('Carol Fullstack', talent_names)

    @patch('talent.views.get_navbar_services')
    def test_talents_list_search_by_skills(self, mock_navbar_services):
        """Test talents_list with skills search in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=React')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return talents with React skills (Bob and Carol)
        self.assertEqual(len(response_data['talents']), 2)
        talent_names = [t['name'] for t in response_data['talents']]
        self.assertIn('Bob Frontend', talent_names)
        self.assertIn('Carol Fullstack', talent_names)

    @patch('talent.views.get_navbar_services')
    def test_talents_list_combined_filters(self, mock_navbar_services):
        """Test talents_list with both role filter and search."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?role=Backend Developer&q=Python')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return Alice (Backend Developer with Python)
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Alice Backend')

        # Check filters
        self.assertEqual(response_data['filters']['role'], 'Backend Developer')
        self.assertEqual(response_data['filters']['search'], 'Python')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_no_results(self, mock_navbar_services):
        """Test talents_list with search that returns no results."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=NonexistentSkill')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return empty list
        self.assertEqual(len(response_data['talents']), 0)
        self.assertEqual(response_data['filters']['search'], 'NonexistentSkill')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_available_roles(self, mock_navbar_services):
        """Test that available_roles contains all unique roles."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Check available roles
        available_roles = response_data['available_roles']
        expected_roles = ['Backend Developer', 'Frontend Developer', 'Full Stack Developer', 'UI/UX Designer']

        for role in expected_roles:
            self.assertIn(role, available_roles)

    @patch('talent.views.get_navbar_services')
    def test_talents_list_data_formatting(self, mock_navbar_services):
        """Test that talents_list formats data correctly."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Find Alice's data
        alice_data = next(t for t in response_data['talents'] if t['name'] == 'Alice Backend')

        # Check data structure and values
        expected_keys = ['id', 'email', 'name', 'image', 'skills', 'average_rating', 'profile', 'created_at']
        for key in expected_keys:
            self.assertIn(key, alice_data)

        # Check specific values
        self.assertEqual(alice_data['email'], '<EMAIL>')
        self.assertEqual(alice_data['skills'], ['Python', 'Django', 'PostgreSQL'])
        self.assertEqual(alice_data['average_rating'], 4.8)
        self.assertTrue(alice_data['profile']['is_available'])

    @patch('talent.views.get_navbar_services')
    def test_talents_list_default_availability(self, mock_navbar_services):
        """Test that is_available defaults to True when not set."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Find Carol's data (doesn't have is_available set)
        carol_data = next(t for t in response_data['talents'] if t['name'] == 'Carol Fullstack')

        # Should default to True
        self.assertTrue(carol_data['profile']['is_available'])

    @patch('talent.views.get_navbar_services')
    def test_talents_list_image_url_handling(self, mock_navbar_services):
        """Test image URL handling in talents_list."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Find talent with image
        dave_data = next(t for t in response_data['talents'] if t['name'] == 'Dave Designer')
        self.assertIsNotNone(dave_data['image'])
        self.assertTrue(dave_data['image'].startswith('http'))

        # Find talent without image
        alice_data = next(t for t in response_data['talents'] if t['name'] == 'Alice Backend')
        self.assertIsNone(alice_data['image'])

    @patch('talent.views.get_navbar_services')
    def test_talents_list_default_rating(self, mock_navbar_services):
        """Test default average_rating when not set in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Find talent with minimal profile
        eve_data = next(t for t in response_data['talents'] if t['name'] == 'Eve Minimal')

        # Should default to 4.5
        self.assertEqual(eve_data['average_rating'], 4.5)

    @patch('talent.views.get_navbar_services')
    def test_talents_list_empty_skills_handling(self, mock_navbar_services):
        """Test handling of missing skills in profile."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Find talent with minimal profile
        eve_data = next(t for t in response_data['talents'] if t['name'] == 'Eve Minimal')

        # Should default to empty list
        self.assertEqual(eve_data['skills'], [])


class TalentViewsEdgeCaseTest(TalentViewsTestCase):
    """Test edge cases and error conditions for talent views."""

    @patch('talent.views.get_navbar_services')
    def test_talents_list_whitespace_search(self, mock_navbar_services):
        """Test talents_list with whitespace-only search query."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=   ')  # Only whitespace

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return all talents (whitespace stripped)
        self.assertEqual(len(response_data['talents']), 5)
        self.assertEqual(response_data['filters']['search'], '')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_empty_role_filter(self, mock_navbar_services):
        """Test talents_list with empty role filter."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?role=')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should return all talents
        self.assertEqual(len(response_data['talents']), 5)
        self.assertEqual(response_data['filters']['role'], '')

    @patch('talent.views.get_navbar_services')
    def test_talents_list_case_insensitive_search(self, mock_navbar_services):
        """Test that search is case insensitive."""
        mock_navbar_services.return_value = {}

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=ALICE')  # Uppercase

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should still find Alice
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'Alice Backend')

    def test_talent_page_url_routing(self):
        """Test that talent page URL routing works correctly."""
        response = self.client.get('/talent/')
        self.assertEqual(response.status_code, 200)

    @patch('talent.views.get_navbar_services')
    def test_navbar_services_integration(self, mock_navbar_services):
        """Test integration with navbar services."""
        expected_services = {
            'what_we_offer': ['Service 1'],
            'technical_expertise': ['Service 2']
        }
        mock_navbar_services.return_value = expected_services

        response = self.client.get(reverse('talent:talent'))

        self.assertEqual(response.context['navbar_services'], expected_services)
        mock_navbar_services.assert_called_once()

    @patch('talent.views.get_navbar_services')
    def test_talents_list_with_special_characters(self, mock_navbar_services):
        """Test search with special characters."""
        mock_navbar_services.return_value = {}

        # Create talent with special characters
        Talent.objects.create(
            email='<EMAIL>',
            name='José María',
            profile={
                'role': 'Developer',
                'bio': 'Specialist in C++ & JavaScript',
                'skills': ['C++', 'JavaScript']
            }
        )

        from django.test import RequestFactory
        factory = RequestFactory()
        request = factory.get('/talents/?q=José')

        from talent.views import talents_list
        response_data = talents_list(request)

        # Should find the talent with special characters
        self.assertEqual(len(response_data['talents']), 1)
        self.assertEqual(response_data['talents'][0]['name'], 'José María')

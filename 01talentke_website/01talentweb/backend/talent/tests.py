from django.test import TestCase
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.postgres.indexes import GinIndex
from talent.models import Talent
from hiring.models import HireRequest
import uuid
from datetime import datetime
from unittest.mock import patch
import tempfile
import os

class TalentModelTest(TestCase):
    def setUp(self):
        """Set up test data."""
        self.talent_data = {
            'email': '<EMAIL>',
            'name': 'Worker Man',
            'profile': {'skills': ['Python', 'React'], 'experience_years': 1},
        }
        self.talent = Talent.objects.create(**self.talent_data)

    def test_talent_creation(self):
        """Test creating a Talent instance."""
        self.assertIsInstance(self.talent.id, uuid.UUID)
        self.assertEqual(self.talent.email, self.talent_data['email'])
        self.assertEqual(self.talent.name, self.talent_data['name'])
        self.assertEqual(self.talent.profile, self.talent_data['profile'])
        self.assertIsNotNone(self.talent.created_at)

    def test_email_unique_constraint(self):
        """Test that email field is unique."""
        with self.assertRaises(IntegrityError):
            Talent.objects.create(
                email=self.talent_data['email'],
                name='Worker Man',
                profile={'skills': ['Django']}
            )

    def test_profile_default(self):
        """Test that profile defaults to empty dict."""
        talent = Talent.objects.create(email='<EMAIL>', name='Worker Man')
        self.assertEqual(talent.profile, {})

    def test_str_method(self):
        """Test the __str__ method."""
        self.assertEqual(str(self.talent), self.talent_data['name'])

    def test_jsonb_profile_query(self):
        """Test querying JSONB profile field."""
        talent = Talent.objects.get(profile__skills__contains=['Python'])
        self.assertEqual(talent.email, self.talent_data['email'])

    def test_indexes(self):
        """Test that indexes are correctly defined."""
        indexes = Talent._meta.indexes
        self.assertEqual(len(indexes), 3)

        index_fields_and_names = [(index.fields, index.name) for index in indexes]

        expected_fields = [
            ['email'],
            ['profile'],
            ['featured_order'],
        ]

        for expected_field in expected_fields:
            found = any(fields == expected_field for fields, _ in index_fields_and_names)
            self.assertTrue(found, f"Index on fields {expected_field} not found")

        self.assertTrue(any(isinstance(idx, GinIndex) and 'profile' in idx.fields for idx in indexes))

    def test_table_name(self):
        """Test that the table name is 'talents'."""
        self.assertEqual(Talent._meta.db_table, 'talents')


class TalentFieldValidationTest(TestCase):
    """Test field validation for the Talent model."""

    def test_email_field_validation(self):
        """Test email field validation and constraints."""
        # Test valid email - create with all required fields
        talent = Talent.objects.create(email='<EMAIL>', name='Test User')
        # Now test validation on the created object
        talent.full_clean()  # Should not raise ValidationError

        # Test invalid email format - test validation directly
        talent_invalid = Talent(email='invalid-email', name='Test User', profile={})
        with self.assertRaises(ValidationError) as cm:
            talent_invalid.full_clean()
        self.assertIn('email', cm.exception.message_dict)

    def test_email_required(self):
        """Test that email field is required."""
        talent = Talent(name='Test User', profile={})
        with self.assertRaises(ValidationError) as cm:
            talent.full_clean()
        self.assertIn('email', cm.exception.message_dict)

    def test_email_unique_constraint(self):
        """Test that email field is unique."""
        Talent.objects.create(email='<EMAIL>', name='User 1')
        with self.assertRaises(IntegrityError):
            Talent.objects.create(email='<EMAIL>', name='User 2')

    def test_name_field_validation(self):
        """Test name field validation and constraints."""
        # Test valid name - create and then validate
        talent = Talent.objects.create(email='<EMAIL>', name='Valid Name')
        talent.full_clean()  # Should not raise ValidationError

        # Test name max length (255 characters)
        long_name = 'a' * 256
        talent_long = Talent(email='<EMAIL>', name=long_name, profile={})
        with self.assertRaises(ValidationError) as cm:
            talent_long.full_clean()
        self.assertIn('name', cm.exception.message_dict)

        # Test name at max length (should be valid)
        max_name = 'a' * 255
        talent_max = Talent.objects.create(email='<EMAIL>', name=max_name)
        talent_max.full_clean()  # Should not raise ValidationError

    def test_name_required(self):
        """Test that name field is required."""
        talent = Talent(email='<EMAIL>', profile={})
        with self.assertRaises(ValidationError) as cm:
            talent.full_clean()
        self.assertIn('name', cm.exception.message_dict)

    def test_profile_field_default(self):
        """Test profile field default value and validation."""
        talent = Talent.objects.create(email='<EMAIL>', name='Test User')
        self.assertEqual(talent.profile, {})

        # Test that profile accepts valid JSON
        talent_with_profile = Talent.objects.create(
            email='<EMAIL>',
            name='Test User 2',
            profile={'skills': ['Python', 'Django'], 'experience': 5}
        )
        self.assertEqual(talent_with_profile.profile['skills'], ['Python', 'Django'])
        self.assertEqual(talent_with_profile.profile['experience'], 5)

    def test_id_field_uuid_generation(self):
        """Test that ID field generates valid UUIDs."""
        talent = Talent.objects.create(email='<EMAIL>', name='Test User')
        self.assertIsInstance(talent.id, uuid.UUID)

        # Test that each talent gets a unique UUID
        talent2 = Talent.objects.create(email='<EMAIL>', name='Test User 2')
        self.assertNotEqual(talent.id, talent2.id)

        # Test that UUID is not editable (should use generated value)
        custom_uuid = uuid.uuid4()
        talent3 = Talent(id=custom_uuid, email='<EMAIL>', name='Test User 3')
        talent3.save()
        # The custom UUID should be preserved since it's set before save
        self.assertEqual(talent3.id, custom_uuid)

    def test_created_at_auto_population(self):
        """Test that created_at is automatically populated."""
        before_creation = datetime.now()
        talent = Talent.objects.create(email='<EMAIL>', name='Test User')
        after_creation = datetime.now()

        self.assertIsNotNone(talent.created_at)
        self.assertGreaterEqual(talent.created_at.replace(tzinfo=None), before_creation)
        self.assertLessEqual(talent.created_at.replace(tzinfo=None), after_creation)

        # Test that created_at doesn't change on update
        original_created_at = talent.created_at
        talent.name = 'Updated Name'
        talent.save()
        talent.refresh_from_db()
        self.assertEqual(talent.created_at, original_created_at)

    def test_featured_order_field(self):
        """Test featured_order field validation."""
        # Test that featured_order can be null
        talent = Talent.objects.create(email='<EMAIL>', name='Test User')
        self.assertIsNone(talent.featured_order)

        # Test that featured_order accepts integers
        talent_featured = Talent.objects.create(
            email='<EMAIL>',
            name='Featured User',
            featured_order=1
        )
        self.assertEqual(talent_featured.featured_order, 1)

        # Test that featured_order can be updated
        talent_featured.featured_order = 5
        talent_featured.save()
        talent_featured.refresh_from_db()
        self.assertEqual(talent_featured.featured_order, 5)

        # Test that featured_order can be set back to null
        talent_featured.featured_order = None
        talent_featured.save()
        talent_featured.refresh_from_db()
        self.assertIsNone(talent_featured.featured_order)


class TalentModelMethodsTest(TestCase):
    """Test model methods and properties for the Talent model."""

    def setUp(self):
        """Set up test data."""
        self.talent = Talent.objects.create(
            email='<EMAIL>',
            name='John Doe',
            profile={'role': 'Developer', 'skills': ['Python', 'Django']}
        )

    def test_str_method_normal_case(self):
        """Test __str__ method returns the name."""
        self.assertEqual(str(self.talent), 'John Doe')

    def test_str_method_empty_name(self):
        """Test __str__ method with empty name."""
        talent_empty_name = Talent.objects.create(
            email='<EMAIL>',
            name=''
        )
        self.assertEqual(str(talent_empty_name), '')

    def test_str_method_unicode_name(self):
        """Test __str__ method with unicode characters."""
        talent_unicode = Talent.objects.create(
            email='<EMAIL>',
            name='José María Aznar'
        )
        self.assertEqual(str(talent_unicode), 'José María Aznar')

    def test_str_method_long_name(self):
        """Test __str__ method with very long name."""
        long_name = 'A' * 255  # Max length
        talent_long = Talent.objects.create(
            email='<EMAIL>',
            name=long_name
        )
        self.assertEqual(str(talent_long), long_name)

    def test_str_method_special_characters(self):
        """Test __str__ method with special characters."""
        special_name = 'John "The Developer" O\'Connor & Associates'
        talent_special = Talent.objects.create(
            email='<EMAIL>',
            name=special_name
        )
        self.assertEqual(str(talent_special), special_name)


class TalentRelationshipTest(TestCase):
    """Test relationships between Talent and other models."""

    def setUp(self):
        """Set up test data."""
        self.talent = Talent.objects.create(
            email='<EMAIL>',
            name='Jane Developer',
            profile={'role': 'Full Stack Developer', 'skills': ['Python', 'React']}
        )

    def test_hire_request_relationship(self):
        """Test the relationship with HireRequest model."""
        # Create hire requests for the talent
        hire_request1 = HireRequest.objects.create(
            talent=self.talent,
            first_name='John',
            last_name='Client',
            email='<EMAIL>',
            subject='Need a developer',
            description='Looking for a full stack developer for our project.'
        )

        hire_request2 = HireRequest.objects.create(
            talent=self.talent,
            first_name='Sarah',
            last_name='Manager',
            email='<EMAIL>',
            subject='React developer needed',
            description='Need someone with React experience.'
        )

        # Test that talent can access related hire requests
        hire_requests = self.talent.hire_requests.all()
        self.assertEqual(hire_requests.count(), 2)
        self.assertIn(hire_request1, hire_requests)
        self.assertIn(hire_request2, hire_requests)

    def test_hire_request_related_name(self):
        """Test the related_name 'hire_requests' works correctly."""
        # Create a hire request
        hire_request = HireRequest.objects.create(
            talent=self.talent,
            first_name='Test',
            last_name='Client',
            email='<EMAIL>',
            subject='Test request',
            description='Test description'
        )

        # Test accessing through related_name
        self.assertEqual(self.talent.hire_requests.count(), 1)
        self.assertEqual(self.talent.hire_requests.first(), hire_request)

    def test_hire_request_cascade_behavior(self):
        """Test SET_NULL cascade behavior when talent is deleted."""
        # Create a hire request
        hire_request = HireRequest.objects.create(
            talent=self.talent,
            first_name='Test',
            last_name='Client',
            email='<EMAIL>',
            subject='Test request',
            description='Test description'
        )

        hire_request_id = hire_request.id

        # Delete the talent
        self.talent.delete()

        # Check that hire request still exists but talent is set to null
        hire_request.refresh_from_db()
        self.assertIsNone(hire_request.talent)
        self.assertEqual(hire_request.id, hire_request_id)

    def test_hire_request_without_talent(self):
        """Test that hire requests can exist without a talent (general inquiries)."""
        # Create a hire request without specifying a talent
        general_request = HireRequest.objects.create(
            first_name='General',
            last_name='Inquiry',
            email='<EMAIL>',
            subject='General hiring inquiry',
            description='Looking for any available developers.'
        )

        self.assertIsNone(general_request.talent)
        self.assertEqual(self.talent.hire_requests.count(), 0)

    def test_multiple_talents_hire_requests(self):
        """Test that multiple talents can have separate hire requests."""
        # Create another talent
        talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='Bob Designer',
            profile={'role': 'UI/UX Designer', 'skills': ['Figma', 'Adobe XD']}
        )

        # Create hire requests for each talent
        request1 = HireRequest.objects.create(
            talent=self.talent,
            first_name='Client',
            last_name='One',
            email='<EMAIL>',
            subject='Need developer',
            description='Developer needed'
        )

        request2 = HireRequest.objects.create(
            talent=talent2,
            first_name='Client',
            last_name='Two',
            email='<EMAIL>',
            subject='Need designer',
            description='Designer needed'
        )

        # Test that each talent has their own hire requests
        self.assertEqual(self.talent.hire_requests.count(), 1)
        self.assertEqual(talent2.hire_requests.count(), 1)
        self.assertEqual(self.talent.hire_requests.first(), request1)
        self.assertEqual(talent2.hire_requests.first(), request2)


class TalentDatabaseConstraintTest(TestCase):
    """Test database constraints and indexes for the Talent model."""

    def test_all_indexes_exist(self):
        """Test that all expected indexes are defined."""
        indexes = Talent._meta.indexes
        self.assertEqual(len(indexes), 3)  # email, profile (GIN), featured_order

        # Get index information
        index_info = []
        for index in indexes:
            if hasattr(index, 'fields'):
                index_info.append({
                    'fields': index.fields,
                    'name': getattr(index, 'name', None),
                    'type': type(index).__name__
                })

        # Check email index
        email_index = next((idx for idx in index_info if idx['fields'] == ['email']), None)
        self.assertIsNotNone(email_index, "Email index not found")
        self.assertEqual(email_index['type'], 'Index')

        # Check profile GIN index
        profile_index = next((idx for idx in index_info if idx['fields'] == ['profile']), None)
        self.assertIsNotNone(profile_index, "Profile index not found")
        self.assertEqual(profile_index['type'], 'GinIndex')
        self.assertEqual(profile_index['name'], 'profile_gin_idx')

        # Check featured_order index
        featured_index = next((idx for idx in index_info if idx['fields'] == ['featured_order']), None)
        self.assertIsNotNone(featured_index, "Featured order index not found")
        self.assertEqual(featured_index['type'], 'Index')

    def test_gin_index_functionality(self):
        """Test that the GIN index on profile field works for JSON queries."""
        # Create talents with different profiles
        talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Python Developer',
            profile={'skills': ['Python', 'Django'], 'role': 'Backend Developer'}
        )

        talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='JS Developer',
            profile={'skills': ['JavaScript', 'React'], 'role': 'Frontend Developer'}
        )

        talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Full Stack Developer',
            profile={'skills': ['Python', 'JavaScript', 'React'], 'role': 'Full Stack Developer'}
        )

        # Test JSON containment queries (these should use the GIN index)
        python_devs = Talent.objects.filter(profile__skills__contains=['Python'])
        self.assertEqual(python_devs.count(), 2)
        self.assertIn(talent1, python_devs)
        self.assertIn(talent3, python_devs)

        js_devs = Talent.objects.filter(profile__skills__contains=['JavaScript'])
        self.assertEqual(js_devs.count(), 2)
        self.assertIn(talent2, js_devs)
        self.assertIn(talent3, js_devs)

        # Test JSON key existence
        role_talents = Talent.objects.filter(profile__has_key='role')
        self.assertEqual(role_talents.count(), 3)

        # Test JSON value lookup
        backend_devs = Talent.objects.filter(profile__role='Backend Developer')
        self.assertEqual(backend_devs.count(), 1)
        self.assertEqual(backend_devs.first(), talent1)

    def test_email_index_functionality(self):
        """Test that the email index improves query performance."""
        # Create multiple talents
        talents = []
        for i in range(10):
            talent = Talent.objects.create(
                email=f'user{i}@example.com',
                name=f'User {i}',
                profile={'role': 'Developer'}
            )
            talents.append(talent)

        # Test email lookup (should use index)
        found_talent = Talent.objects.get(email='<EMAIL>')
        self.assertEqual(found_talent, talents[5])

        # Test email filtering
        filtered_talents = Talent.objects.filter(email__in=['<EMAIL>', '<EMAIL>'])
        self.assertEqual(filtered_talents.count(), 2)

    def test_featured_order_index_functionality(self):
        """Test that the featured_order index works for ordering queries."""
        # Create talents with different featured orders
        talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Featured 1',
            featured_order=1
        )

        talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='Featured 2',
            featured_order=3
        )

        talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Featured 3',
            featured_order=2
        )

        # Create non-featured talent
        Talent.objects.create(
            email='<EMAIL>',
            name='Regular User'
        )

        # Test ordering by featured_order (should use index)
        featured_talents = Talent.objects.filter(
            featured_order__isnull=False
        ).order_by('featured_order')

        self.assertEqual(featured_talents.count(), 3)
        self.assertEqual(list(featured_talents), [talent1, talent3, talent2])

        # Test filtering by featured_order
        top_featured = Talent.objects.filter(featured_order__lte=2).order_by('featured_order')
        self.assertEqual(top_featured.count(), 2)
        self.assertEqual(list(top_featured), [talent1, talent3])

    def test_database_table_name(self):
        """Test that the model uses the correct database table name."""
        self.assertEqual(Talent._meta.db_table, 'talents')

    def test_model_meta_configuration(self):
        """Test model Meta class configuration."""
        meta = Talent._meta

        # Test table name
        self.assertEqual(meta.db_table, 'talents')

        # Test that model has the expected fields
        field_names = [field.name for field in meta.fields]
        expected_fields = ['id', 'email', 'name', 'profile', 'image', 'created_at', 'featured_order']
        for field_name in expected_fields:
            self.assertIn(field_name, field_names)

    def test_unique_constraint_enforcement(self):
        """Test that unique constraints are properly enforced at database level."""
        from django.db import transaction

        # Create first talent
        Talent.objects.create(email='<EMAIL>', name='First User')

        # Try to create second talent with same email - should fail
        with transaction.atomic():
            with self.assertRaises(IntegrityError):
                Talent.objects.create(email='<EMAIL>', name='Second User')

        # Verify only one talent exists with that email
        self.assertEqual(Talent.objects.filter(email='<EMAIL>').count(), 1)


class TalentEdgeCaseTest(TestCase):
    """Test edge cases and error conditions for the Talent model."""

    def test_empty_profile_json(self):
        """Test handling of empty and various JSON profile values."""
        # Test empty dict (default)
        talent1 = Talent.objects.create(email='<EMAIL>', name='Empty Profile')
        self.assertEqual(talent1.profile, {})

        # Test that profile field doesn't accept None (due to database constraint)
        from django.db import transaction
        with transaction.atomic():
            with self.assertRaises(IntegrityError):
                Talent.objects.create(email='<EMAIL>', name='Null Profile', profile=None)

        # Test complex nested JSON
        complex_profile = {
            'personal': {
                'age': 30,
                'location': 'Nairobi, Kenya'
            },
            'skills': ['Python', 'Django', 'React'],
            'experience': [
                {'company': 'TechCorp', 'years': 2},
                {'company': 'StartupXYZ', 'years': 1}
            ],
            'certifications': None,
            'available': True
        }
        talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Complex Profile',
            profile=complex_profile
        )
        self.assertEqual(talent3.profile, complex_profile)

    def test_boundary_values_featured_order(self):
        """Test boundary values for featured_order field."""
        # Test very large positive number
        talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Large Order',
            featured_order=2147483647  # Max 32-bit signed integer
        )
        self.assertEqual(talent1.featured_order, 2147483647)

        # Test zero
        talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='Zero Order',
            featured_order=0
        )
        self.assertEqual(talent2.featured_order, 0)

        # Test negative number
        talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Negative Order',
            featured_order=-1
        )
        self.assertEqual(talent3.featured_order, -1)

    def test_email_edge_cases(self):
        """Test edge cases for email field."""
        # Test very long email (within limits)
        long_local = 'a' * 50
        long_domain = 'b' * 50 + '.com'
        long_email = f'{long_local}@{long_domain}'

        if len(long_email) <= 254:  # Django EmailField max length
            talent = Talent.objects.create(
                email=long_email,
                name='Long Email User'
            )
            self.assertEqual(talent.email, long_email)

        # Test email with special characters
        special_email = '<EMAIL>'
        talent_special = Talent.objects.create(
            email=special_email,
            name='Special Email User'
        )
        self.assertEqual(talent_special.email, special_email)

        # Test email case sensitivity (PostgreSQL email field is case sensitive by default)
        Talent.objects.create(email='<EMAIL>', name='Lower Case')
        # This should succeed as PostgreSQL treats these as different emails
        Talent.objects.create(email='<EMAIL>', name='Upper Case')
        self.assertNotEqual('<EMAIL>', '<EMAIL>')

    def test_name_edge_cases(self):
        """Test edge cases for name field."""
        # Test single character name
        talent1 = Talent.objects.create(email='<EMAIL>', name='A')
        self.assertEqual(talent1.name, 'A')

        # Test name with only spaces (valid but unusual)
        talent2 = Talent.objects.create(email='<EMAIL>', name='   ')
        self.assertEqual(talent2.name, '   ')

        # Test name with numbers and special characters
        special_name = 'John123 O\'Connor-Smith Jr. (PhD)'
        talent3 = Talent.objects.create(email='<EMAIL>', name=special_name)
        self.assertEqual(talent3.name, special_name)

        # Test unicode characters in name
        unicode_name = 'José María Ñoño'
        talent4 = Talent.objects.create(email='<EMAIL>', name=unicode_name)
        self.assertEqual(talent4.name, unicode_name)

    def test_concurrent_creation_edge_cases(self):
        """Test edge cases that might occur with concurrent operations."""
        # Test creating talents with same email simultaneously (should fail)
        email = '<EMAIL>'

        # First creation should succeed
        talent1 = Talent.objects.create(email=email, name='First')
        self.assertEqual(talent1.email, email)

        # Second creation should fail due to unique constraint
        with self.assertRaises(IntegrityError):
            Talent.objects.create(email=email, name='Second')

    def test_json_field_query_edge_cases(self):
        """Test edge cases for JSON field queries."""
        # Create talent with complex profile
        talent = Talent.objects.create(
            email='<EMAIL>',
            name='JSON Test',
            profile={
                'skills': ['Python', 'Django'],
                'nested': {'deep': {'value': 'test'}},
                'null_value': None,
                'empty_list': [],
                'empty_dict': {},
                'boolean': True,
                'number': 42
            }
        )

        # Test querying null values in JSON (PostgreSQL treats JSON null differently)
        # In PostgreSQL, JSON null values are not the same as SQL NULL
        # So we test for the presence of the key instead
        null_talents = Talent.objects.filter(profile__has_key='null_value')
        self.assertIn(talent, null_talents)

        # Test querying boolean values
        bool_talents = Talent.objects.filter(profile__boolean=True)
        self.assertIn(talent, bool_talents)

        # Test querying numeric values
        num_talents = Talent.objects.filter(profile__number=42)
        self.assertIn(talent, num_talents)

        # Test deep nested queries
        deep_talents = Talent.objects.filter(profile__nested__deep__value='test')
        self.assertIn(talent, deep_talents)

        # Test querying empty collections
        empty_list_talents = Talent.objects.filter(profile__empty_list=[])
        self.assertIn(talent, empty_list_talents)

    def test_model_validation_edge_cases(self):
        """Test model validation with edge case data."""
        # Test validation with minimal required data - create first then validate
        talent = Talent.objects.create(email='<EMAIL>', name='Minimal')
        try:
            talent.full_clean()  # Should not raise ValidationError
        except ValidationError:
            self.fail("Minimal valid data should pass validation")

        # Test validation with all fields populated
        talent_full = Talent.objects.create(
            email='<EMAIL>',
            name='Full Data',
            profile={'complete': True},
            featured_order=1
        )
        try:
            talent_full.full_clean()  # Should not raise ValidationError
        except ValidationError:
            self.fail("Complete valid data should pass validation")

    def test_string_representation_edge_cases(self):
        """Test string representation with edge case names."""
        # Test the __str__ method by temporarily setting name to None after creation
        talent_created = Talent.objects.create(email='<EMAIL>', name='Test')
        # Setting name to None would cause issues, so test with empty string instead
        talent_created.name = ''
        self.assertEqual(str(talent_created), '')


class TalentImageFieldTest(TestCase):
    """Test image field functionality for the Talent model."""

    def setUp(self):
        """Set up test data."""
        self.talent = Talent.objects.create(
            email='<EMAIL>',
            name='Image Test User'
        )

    def tearDown(self):
        """Clean up uploaded files after tests."""
        # Clean up any uploaded files
        if self.talent.image:
            if os.path.exists(self.talent.image.path):
                os.remove(self.talent.image.path)

    def test_image_field_null_by_default(self):
        """Test that image field is null by default."""
        self.assertFalse(self.talent.image.name)
        self.assertFalse(self.talent.image)

    def test_image_field_upload_path(self):
        """Test that image field uses correct upload path."""
        # Create a simple test image file
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'

        uploaded_file = SimpleUploadedFile(
            name='test_image.png',
            content=image_content,
            content_type='image/png'
        )

        self.talent.image = uploaded_file
        self.talent.save()

        # Check that the file path starts with 'talents/'
        self.assertTrue(self.talent.image.name.startswith('talents/'))
        self.assertTrue(self.talent.image.name.endswith('.png'))

    def test_image_field_blank_and_null(self):
        """Test that image field can be blank and null."""
        # Test creating talent without image
        talent_no_image = Talent.objects.create(
            email='<EMAIL>',
            name='No Image User'
        )
        self.assertFalse(talent_no_image.image.name)

        # Test setting image to None (which is allowed)
        talent_no_image.image = None
        talent_no_image.save()
        talent_no_image.refresh_from_db()
        self.assertFalse(talent_no_image.image.name)

    @patch('django.core.files.storage.default_storage.save')
    def test_image_field_file_saving(self, mock_save):
        """Test that image files are properly saved."""
        mock_save.return_value = 'talents/test_image.png'

        uploaded_file = SimpleUploadedFile(
            name='test_image.png',
            content=b'fake image content',
            content_type='image/png'
        )

        self.talent.image = uploaded_file
        self.talent.save()

        # Verify that the storage save method was called
        mock_save.assert_called_once()

        # Verify the file name was set
        self.assertEqual(self.talent.image.name, 'talents/test_image.png')

    def test_image_field_different_formats(self):
        """Test image field with different image formats."""
        # Test with JPEG
        jpeg_file = SimpleUploadedFile(
            name='test.jpg',
            content=b'fake jpeg content',
            content_type='image/jpeg'
        )

        talent_jpeg = Talent.objects.create(
            email='<EMAIL>',
            name='JPEG User',
            image=jpeg_file
        )
        self.assertTrue(talent_jpeg.image.name.endswith('.jpg'))

        # Test with GIF
        gif_file = SimpleUploadedFile(
            name='test.gif',
            content=b'fake gif content',
            content_type='image/gif'
        )

        talent_gif = Talent.objects.create(
            email='<EMAIL>',
            name='GIF User',
            image=gif_file
        )
        self.assertTrue(talent_gif.image.name.endswith('.gif'))

    def test_image_field_update(self):
        """Test updating the image field."""
        # First, add an image
        first_image = SimpleUploadedFile(
            name='first.png',
            content=b'first image content',
            content_type='image/png'
        )

        self.talent.image = first_image
        self.talent.save()
        first_image_name = self.talent.image.name

        # Then update with a new image
        second_image = SimpleUploadedFile(
            name='second.png',
            content=b'second image content',
            content_type='image/png'
        )

        self.talent.image = second_image
        self.talent.save()

        # Verify the image was updated
        self.assertNotEqual(self.talent.image.name, first_image_name)
        self.assertTrue(self.talent.image.name.endswith('.png'))

    def test_image_field_deletion(self):
        """Test deleting talent with image."""
        # Add an image
        image_file = SimpleUploadedFile(
            name='delete_test.png',
            content=b'image to delete',
            content_type='image/png'
        )

        self.talent.image = image_file
        self.talent.save()

        # Delete the talent
        talent_id = self.talent.id
        self.talent.delete()

        # Verify talent is deleted
        self.assertFalse(Talent.objects.filter(id=talent_id).exists())

        # Note: Django doesn't automatically delete files when model instances are deleted
        # This is by design for safety reasons

    def test_image_field_with_special_characters(self):
        """Test image field with special characters in filename."""
        special_file = SimpleUploadedFile(
            name='test image with spaces & symbols!.png',
            content=b'special filename content',
            content_type='image/png'
        )

        talent_special = Talent.objects.create(
            email='<EMAIL>',
            name='Special File User',
            image=special_file
        )

        # Django should handle special characters in filenames
        self.assertIsNotNone(talent_special.image.name)
        self.assertTrue(talent_special.image.name.startswith('talents/'))

    def test_image_field_large_filename(self):
        """Test image field with very long filename."""
        long_filename = 'a' * 200 + '.png'  # Very long filename

        long_file = SimpleUploadedFile(
            name=long_filename,
            content=b'long filename content',
            content_type='image/png'
        )

        talent_long = Talent.objects.create(
            email='<EMAIL>',
            name='Long Filename User',
            image=long_file
        )

        # Django should handle long filenames (may truncate or modify)
        self.assertIsNotNone(talent_long.image.name)
        self.assertTrue(talent_long.image.name.startswith('talents/'))


class TalentJSONFieldQueryTest(TestCase):
    """Test advanced JSON field querying capabilities for the Talent model."""

    def setUp(self):
        """Set up test data with various profile structures."""
        self.talent1 = Talent.objects.create(
            email='<EMAIL>',
            name='Backend Developer',
            profile={
                'role': 'Backend Developer',
                'skills': ['Python', 'Django', 'PostgreSQL', 'Redis', 'Docker'],
                'experience_years': 5,
                'location': 'Nairobi',
                'availability': True,
                'salary_range': {'min': 80000, 'max': 120000},
                'education': {
                    'degree': 'Computer Science',
                    'university': 'University of Nairobi',
                    'graduation_year': 2018
                },
                'projects': [
                    {'name': 'E-commerce API', 'tech': ['Django', 'PostgreSQL']},
                    {'name': 'Analytics Dashboard', 'tech': ['Python', 'Redis']}
                ]
            }
        )

        self.talent2 = Talent.objects.create(
            email='<EMAIL>',
            name='Frontend Developer',
            profile={
                'role': 'Frontend Developer',
                'skills': ['JavaScript', 'React', 'TypeScript', 'CSS'],
                'experience_years': 3,
                'location': 'Mombasa',
                'availability': False,
                'salary_range': {'min': 60000, 'max': 90000},
                'education': {
                    'degree': 'Information Technology',
                    'university': 'Technical University of Mombasa',
                    'graduation_year': 2020
                },
                'projects': [
                    {'name': 'React Dashboard', 'tech': ['React', 'TypeScript']},
                    {'name': 'Mobile App UI', 'tech': ['React Native', 'JavaScript']}
                ]
            }
        )

        self.talent3 = Talent.objects.create(
            email='<EMAIL>',
            name='Full Stack Developer',
            profile={
                'role': 'Full Stack Developer',
                'skills': ['Python', 'JavaScript', 'React', 'Django', 'Node.js'],
                'experience_years': 7,
                'location': 'Nairobi',
                'availability': True,
                'salary_range': {'min': 100000, 'max': 150000},
                'education': {
                    'degree': 'Software Engineering',
                    'university': 'Strathmore University',
                    'graduation_year': 2016
                },
                'certifications': ['AWS Certified', 'Google Cloud Professional'],
                'projects': [
                    {'name': 'Full Stack App', 'tech': ['React', 'Django', 'PostgreSQL']},
                    {'name': 'Microservices API', 'tech': ['Node.js', 'MongoDB']}
                ]
            }
        )

    def test_json_field_exact_match(self):
        """Test exact match queries on JSON fields."""
        # Test exact role match
        backend_devs = Talent.objects.filter(profile__role='Backend Developer')
        self.assertEqual(backend_devs.count(), 1)
        self.assertEqual(backend_devs.first(), self.talent1)

        # Test exact numeric match
        senior_devs = Talent.objects.filter(profile__experience_years=7)
        self.assertEqual(senior_devs.count(), 1)
        self.assertEqual(senior_devs.first(), self.talent3)

        # Test exact boolean match
        available_devs = Talent.objects.filter(profile__availability=True)
        self.assertEqual(available_devs.count(), 2)
        self.assertIn(self.talent1, available_devs)
        self.assertIn(self.talent3, available_devs)

    def test_json_field_contains_queries(self):
        """Test containment queries on JSON arrays."""
        # Test skills containment
        python_devs = Talent.objects.filter(profile__skills__contains=['Python'])
        self.assertEqual(python_devs.count(), 2)
        self.assertIn(self.talent1, python_devs)
        self.assertIn(self.talent3, python_devs)

        # Test multiple skills containment
        django_python_devs = Talent.objects.filter(
            profile__skills__contains=['Python', 'Django']
        )
        self.assertEqual(django_python_devs.count(), 2)

        # Test single skill containment
        react_devs = Talent.objects.filter(profile__skills__contains=['React'])
        self.assertEqual(react_devs.count(), 2)
        self.assertIn(self.talent2, react_devs)
        self.assertIn(self.talent3, react_devs)

    def test_json_field_nested_queries(self):
        """Test queries on nested JSON objects."""
        # Test nested object field queries
        cs_graduates = Talent.objects.filter(
            profile__education__degree='Computer Science'
        )
        self.assertEqual(cs_graduates.count(), 1)
        self.assertEqual(cs_graduates.first(), self.talent1)

        # Test nested numeric queries
        recent_graduates = Talent.objects.filter(
            profile__education__graduation_year__gte=2020
        )
        self.assertEqual(recent_graduates.count(), 1)
        self.assertEqual(recent_graduates.first(), self.talent2)

        # Test nested range queries
        high_salary_devs = Talent.objects.filter(
            profile__salary_range__max__gte=120000
        )
        self.assertEqual(high_salary_devs.count(), 2)

    def test_json_field_has_key_queries(self):
        """Test has_key queries for JSON fields."""
        # Test for presence of specific keys
        certified_devs = Talent.objects.filter(profile__has_key='certifications')
        self.assertEqual(certified_devs.count(), 1)
        self.assertEqual(certified_devs.first(), self.talent3)

        # Test for presence of nested keys
        educated_devs = Talent.objects.filter(profile__has_key='education')
        self.assertEqual(educated_devs.count(), 3)

        # Test for absence of keys
        non_certified_devs = Talent.objects.exclude(profile__has_key='certifications')
        self.assertEqual(non_certified_devs.count(), 2)

    def test_json_field_has_any_keys(self):
        """Test has_any_keys queries for JSON fields."""
        # Test for any of multiple keys
        devs_with_certs_or_projects = Talent.objects.filter(
            profile__has_any_keys=['certifications', 'projects']
        )
        self.assertEqual(devs_with_certs_or_projects.count(), 3)  # All have projects

    def test_json_field_has_keys(self):
        """Test has_keys queries for JSON fields."""
        # Test for all specified keys
        complete_profiles = Talent.objects.filter(
            profile__has_keys=['role', 'skills', 'experience_years']
        )
        self.assertEqual(complete_profiles.count(), 3)

        # Test for specific combination of keys
        detailed_profiles = Talent.objects.filter(
            profile__has_keys=['education', 'projects', 'salary_range']
        )
        self.assertEqual(detailed_profiles.count(), 3)

    def test_json_field_array_length(self):
        """Test array length queries on JSON fields using raw SQL."""
        from django.db import connection

        # PostgreSQL JSON array length queries require raw SQL
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) FROM talents
                WHERE jsonb_array_length(profile->'skills') >= 4
            """)
            multi_skilled_count = cursor.fetchone()[0]
            self.assertEqual(multi_skilled_count, 3)

            cursor.execute("""
                SELECT COUNT(*) FROM talents
                WHERE jsonb_array_length(profile->'skills') = 4
            """)
            four_skills_count = cursor.fetchone()[0]
            self.assertEqual(four_skills_count, 1)

    def test_json_field_array_index_access(self):
        """Test accessing array elements by index."""
        # Test first skill
        python_first_skill = Talent.objects.filter(profile__skills__0='Python')
        self.assertEqual(python_first_skill.count(), 2)

        # Test second skill
        django_second_skill = Talent.objects.filter(profile__skills__1='Django')
        self.assertEqual(django_second_skill.count(), 1)
        self.assertEqual(django_second_skill.first(), self.talent1)

    def test_json_field_complex_queries(self):
        """Test complex combinations of JSON field queries."""
        # Combine multiple JSON field conditions
        senior_python_nairobi = Talent.objects.filter(
            profile__skills__contains=['Python'],
            profile__experience_years__gte=5,
            profile__location='Nairobi'
        )
        self.assertEqual(senior_python_nairobi.count(), 2)

        # Combine JSON and regular field queries
        available_react_devs = Talent.objects.filter(
            profile__skills__contains=['React'],
            profile__availability=True,
            name__icontains='Developer'
        )
        self.assertEqual(available_react_devs.count(), 1)
        self.assertEqual(available_react_devs.first(), self.talent3)

    def test_json_field_ordering(self):
        """Test ordering by JSON field values."""
        # Order by experience years
        by_experience = Talent.objects.order_by('profile__experience_years')
        experience_years = [t.profile['experience_years'] for t in by_experience]
        self.assertEqual(experience_years, [3, 5, 7])

        # Order by nested field
        by_graduation = Talent.objects.order_by('profile__education__graduation_year')
        graduation_years = [t.profile['education']['graduation_year'] for t in by_graduation]
        self.assertEqual(graduation_years, [2016, 2018, 2020])

    def test_json_field_aggregation(self):
        """Test aggregation on JSON field values using raw SQL."""
        from django.db import connection

        # PostgreSQL JSON aggregation requires casting to numeric types
        with connection.cursor() as cursor:
            # Test average experience years
            cursor.execute("""
                SELECT AVG((profile->>'experience_years')::int)
                FROM talents
                WHERE profile ? 'experience_years'
            """)
            avg_experience = cursor.fetchone()[0]
            self.assertEqual(float(avg_experience), 5.0)  # (3+5+7)/3 = 5

            # Test max/min salary ranges
            cursor.execute("""
                SELECT
                    MAX((profile->'salary_range'->>'max')::int) as max_salary,
                    MIN((profile->'salary_range'->>'min')::int) as min_salary
                FROM talents
                WHERE profile ? 'salary_range'
            """)
            max_salary, min_salary = cursor.fetchone()
            self.assertEqual(max_salary, 150000)
            self.assertEqual(min_salary, 60000)

    def test_json_field_isnull_queries(self):
        """Test null/not null queries on JSON fields."""
        # Test for null values in JSON
        talents_with_certs = Talent.objects.filter(
            profile__certifications__isnull=False
        )
        self.assertEqual(talents_with_certs.count(), 1)

        # Test for missing keys (treated as null)
        talents_without_certs = Talent.objects.filter(
            profile__certifications__isnull=True
        )
        self.assertEqual(talents_without_certs.count(), 2)
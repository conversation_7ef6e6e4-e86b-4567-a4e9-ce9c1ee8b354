#!/usr/bin/env python
"""
Test script to check if admin dashboard components are accessible.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zone01web.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client

def test_admin_routes():
    """Test all admin dashboard routes."""
    
    print("🧪 Testing Admin Dashboard Routes...")
    
    # Create test client
    client = Client()
    
    # Create test superuser if doesn't exist
    user, created = User.objects.get_or_create(
        username='testadmin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    
    # Login
    client.login(username='testadmin', password='testpass123')
    
    # Test routes
    routes_to_test = [
        ('/admin-dashboard/', 'Dashboard'),
        ('/admin-dashboard/talents/', 'Talents List'),
        ('/admin-dashboard/talents/create/', 'Talent Create'),
        ('/admin-dashboard/services/', 'Services List'),
        ('/admin-dashboard/services/create/', 'Service Create'),
        ('/admin-dashboard/hire-requests/', 'Hire Requests List'),
        ('/admin-dashboard/contacts/', 'Contacts List'),
        ('/admin-dashboard/statistics/', 'Statistics List'),
        ('/admin-dashboard/statistics/create/', 'Statistics Create'),
        ('/admin-dashboard/about/', 'About List'),
        ('/admin-dashboard/about/create/', 'About Create'),
        ('/admin-dashboard/clients/', 'Clients List'),
        ('/admin-dashboard/clients/create/', 'Clients Create'),
        ('/admin-dashboard/blogs/', 'Blogs List'),
        ('/admin-dashboard/blogs/create/', 'Blogs Create'),
    ]
    
    for route, name in routes_to_test:
        try:
            response = client.get(route)
            if response.status_code == 200:
                print(f"   ✅ {name}: {route}")
            else:
                print(f"   ❌ {name}: {route} (Status: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {name}: {route} (Error: {e})")
    
    print("\n🎉 Route testing completed!")

if __name__ == '__main__':
    test_admin_routes()

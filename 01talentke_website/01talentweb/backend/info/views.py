from inertia import inertia
from .models import About
from services.views import get_navbar_services

@inertia('About/Index')
def about_page(request):
    about = About.objects.last()
    data = {
        'title': about.title if about else '',
        'content': about.content if about else '',
        'image': request.build_absolute_uri(about.image.url) if about and about.image else None,
        'updated_at': about.updated_at if about else None,
    }
    return {
        'about': data,
        'navbar_services': get_navbar_services(request)
    }
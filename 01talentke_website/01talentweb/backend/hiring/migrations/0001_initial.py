# Generated by Django 4.2.2 on 2025-07-18 12:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('talent', '0003_talent_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='HireRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.Char<PERSON>ield(max_length=100)),
                ('last_name', models.Char<PERSON>ield(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('company', models.CharField(blank=True, max_length=100, null=True)),
                ('subject', models.Char<PERSON>ield(max_length=200)),
                ('description', models.TextField()),
                ('status', models.Char<PERSON><PERSON>(choices=[('pending', 'Pending'), ('reviewed', 'Reviewed'), ('contacted', 'Contacted'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('talent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hire_requests', to='talent.talent')),
            ],
            options={
                'verbose_name': 'Hire Request',
                'verbose_name_plural': 'Hire Requests',
                'ordering': ['-created_at'],
            },
        ),
    ]

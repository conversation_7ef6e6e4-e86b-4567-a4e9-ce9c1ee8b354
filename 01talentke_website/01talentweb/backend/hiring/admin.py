# hire/admin.py
from django.contrib import admin
from .models import HireRequest

@admin.register(HireRequest)
class HireRequestAdmin(admin.ModelAdmin):
    list_display = ('id', 'talent', 'first_name', 'last_name', 'email', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('first_name', 'last_name', 'email', 'company', 'subject')
    list_editable = ('status',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Request Information', {
            'fields': ('talent', 'status', 'subject', 'description')
        }),
        ('Client Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'company')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
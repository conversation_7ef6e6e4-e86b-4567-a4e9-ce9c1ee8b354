from django.db import models
from talent.models import Talent

class HireRequest(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('reviewed', 'Reviewed'),
        ('contacted', 'Contacted'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]

    talent = models.ForeignKey(
        Talent,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='hire_requests'
    )
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True)
    company = models.CharField(max_length=100, blank=True, null=True)
    subject = models.Char<PERSON><PERSON>(max_length=200)
    description = models.TextField()
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Hire Request'
        verbose_name_plural = 'Hire Requests'

    def __str__(self):
        talent_name = self.talent.name if self.talent else "General"
        return f"Hire request for {talent_name} from {self.first_name} {self.last_name}"
from django.core.mail import send_mail
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from talent.models import Talent
from .models import HireRequest

class SubmitHireRequest(APIView):
    def post(self, request):
        data = request.data
        talent_id = data.get('talent_id')
        
        try:
            # Validate required fields
            required_fields = ['first_name', 'last_name', 'email', 'subject', 'description']
            for field in required_fields:
                if not data.get(field):
                    return Response(
                        {'error': f'{field} is required'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Get talent if specified
            talent = None
            if talent_id:
                try:
                    talent = Talent.objects.get(id=talent_id)
                except Talent.DoesNotExist:
                    return Response(
                        {'error': 'Talent not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            
            # Create hire request record
            hire_request = HireRequest.objects.create(
                talent=talent,
                first_name=data['first_name'],
                last_name=data['last_name'],
                email=data['email'],
                phone=data.get('phone'),
                company=data.get('company'),
                subject=data['subject'],
                description=data['description'],
                status='pending'
            )
            
            # Send email to talent manager
            self._send_notification_email(hire_request)
            
            # Send confirmation to client
            self._send_confirmation_email(hire_request)
            
            return Response(
                {'message': 'Request submitted successfully'},
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _send_notification_email(self, hire_request):
        """Send email to talent manager about the new request"""
        subject = (f"New Hire Request for {hire_request.talent.name}" 
                  if hire_request.talent else "New General Hire Request")
        
        content = f"""
        New hire request received:
        
        Name: {hire_request.first_name} {hire_request.last_name}
        Email: {hire_request.email}
        Phone: {hire_request.phone or 'Not provided'}
        Company: {hire_request.company or 'Not provided'}
        
        Project Subject: {hire_request.subject}
        Project Description:
        {hire_request.description}
        """
        
        if hire_request.talent:
            content += f"\n\nRequested Talent: {hire_request.talent.name} (ID: {hire_request.talent.id})"
        
        send_mail(
            subject,
            content,
            settings.DEFAULT_FROM_EMAIL,
            [settings.TALENT_MANAGER_EMAIL],
            fail_silently=False,
        )
    
    def _send_confirmation_email(self, hire_request):
        """Send confirmation email to the client"""
        subject = "Thank you for your hire request"
        
        if hire_request.talent:
            content = f"""
            Hi {hire_request.first_name},
            
            Thank you for your interest in hiring {hire_request.talent.name}.
            We've received your request and will get back to you shortly.
            
            Project: {hire_request.subject}
            
            Best regards,
            The Talent Team
            """
        else:
            content = f"""
            Hi {hire_request.first_name},
            
            Thank you for your hire request.
            We've received your details and will match you with the perfect talent shortly.
            
            Project: {hire_request.subject}
            
            Best regards,
            The Talent Team
            """
        
        send_mail(
            subject,
            content,
            settings.DEFAULT_FROM_EMAIL,
            [hire_request.email],
            fail_silently=False,
        )
from django.views.generic import View
from django.contrib.auth.mixins import LoginRequiredMixin
from inertia import render
from .models import HireRequest

class HireRequestListView(LoginRequiredMixin, View):
    def get(self, request):
        hire_requests = HireRequest.objects.all().order_by('-created_at')
        status_filter = request.GET.get('status')
        
        if status_filter:
            hire_requests = hire_requests.filter(status=status_filter)
        
        return render(request, 'HireRequest/List', {
            'hireRequests': [
                {
                    'id': hr.id,
                    'talent': {
                        'id': hr.talent.id,
                        'name': hr.talent.name
                    } if hr.talent else None,
                    'firstName': hr.first_name,
                    'lastName': hr.last_name,
                    'email': hr.email,
                    'subject': hr.subject,
                    'status': hr.status,
                    'statusDisplay': hr.get_status_display(),
                    'createdAt': hr.created_at,
                }
                for hr in hire_requests
            ],
            'filters': {
                'status': status_filter
            },
            'statusOptions': dict(HireRequest.STATUS_CHOICES)
        })

class HireRequestDetailView(LoginRequiredMixin, View):
    def get(self, request, pk):
        hire_request = HireRequest.objects.get(pk=pk)
        
        return render(request, 'HireRequest/Detail', {
            'hireRequest': {
                'id': hire_request.id,
                'talent': {
                    'id': hire_request.talent.id,
                    'name': hire_request.talent.name
                } if hire_request.talent else None,
                'firstName': hire_request.first_name,
                'lastName': hire_request.last_name,
                'email': hire_request.email,
                'phone': hire_request.phone,
                'company': hire_request.company,
                'subject': hire_request.subject,
                'description': hire_request.description,
                'status': hire_request.status,
                'statusDisplay': hire_request.get_status_display(),
                'createdAt': hire_request.created_at,
                'updatedAt': hire_request.updated_at,
            }
        })
import os
import django
import random
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zone01web.settings')
django.setup()

from talent.models import Talent

# Sample data for generating realistic profiles
roles = [
    "Full Stack Developer",
    "Frontend Developer",
    "Backend Developer",
    "DevOps Engineer",
    "Mobile Developer",
    "Data Scientist",
    "UI/UX Developer",
    "Cloud Engineer",
    "Security Engineer",
    "Machine Learning Engineer"
]

tech_stacks = {
    "Full Stack Developer": ["Python", "JavaScript", "React", "Django", "PostgreSQL", "Docker", "AWS"],
    "Frontend Developer": ["JavaScript", "React", "Vue.js", "HTML5", "CSS3", "TypeScript", "Tailwind CSS"],
    "Backend Developer": ["Python", "Django", "FastAPI", "PostgreSQL", "Redis", "GraphQL", "Docker"],
    "DevOps Engineer": ["Docker", "Kubernetes", "AWS", "Jenkins", "Terraform", "Linux", "CI/CD"],
    "Mobile Developer": ["React Native", "Flutter", "Kotlin", "Swift", "Firebase", "REST APIs"],
    "Data Scientist": ["Python", "R", "TensorFlow", "PyTorch", "SQL", "Pandas", "Scikit-learn"],
    "UI/UX Developer": ["Figma", "Adobe XD", "JavaScript", "React", "CSS3", "User Research"],
    "Cloud Engineer": ["AWS", "Azure", "GCP", "Terraform", "Docker", "Kubernetes", "CloudFormation"],
    "Security Engineer": ["Python", "Network Security", "Cryptography", "Penetration Testing", "Security Auditing"],
    "Machine Learning Engineer": ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "Computer Vision", "NLP"]
}

companies = ["Google", "Microsoft", "Amazon", "Meta", "Apple", "Netflix", "Uber", "Twitter", "Airbnb", "LinkedIn"]

# Sample developers data
developers = [
    {
        "name": "Sarah Johnson",
        "email": "<EMAIL>",
        "profile": {
            "role": "Full Stack Developer",
            "bio": "Passionate full-stack developer with 5 years of experience building scalable web applications.",
            "years_of_experience": 5,
            "is_available": True,
            "preferred_work_type": "Remote",
            "github": "https://github.com/sarahjdev",
            "linkedin": "https://linkedin.com/in/sarahjohnson"
        }
    },
    {
        "name": "Michael Chen",
        "email": "<EMAIL>",
        "profile": {
            "role": "Frontend Developer",
            "bio": "Creative frontend developer specializing in responsive and accessible web applications.",
            "years_of_experience": 3,
            "is_available": False,
            "preferred_work_type": "Hybrid",
            "github": "https://github.com/mchendev",
            "linkedin": "https://linkedin.com/in/michaelchen"
        }
    },
    {
        "name": "David Kumar",
        "email": "<EMAIL>",
        "profile": {
            "role": "Backend Developer",
            "bio": "Backend specialist with strong focus on API design and database optimization.",
            "years_of_experience": 4,
            "is_available": True,
            "preferred_work_type": "On-site",
            "github": "https://github.com/dkumar",
            "linkedin": "https://linkedin.com/in/davidkumar"
        }
    },
    {
        "name": "Emma Rodriguez",
        "email": "<EMAIL>",
        "profile": {
            "role": "DevOps Engineer",
            "bio": "DevOps engineer passionate about automating everything and improving deployment processes.",
            "years_of_experience": 6,
            "is_available": True,
            "preferred_work_type": "Remote",
            "github": "https://github.com/emmarodriguez",
            "linkedin": "https://linkedin.com/in/emmarodriguez"
        }
    },
    {
        "name": "James Wilson",
        "email": "<EMAIL>",
        "profile": {
            "role": "Mobile Developer",
            "bio": "Mobile app developer specialized in creating cross-platform applications using React Native.",
            "years_of_experience": 4,
            "is_available": False,
            "preferred_work_type": "Remote",
            "github": "https://github.com/jwilson",
            "linkedin": "https://linkedin.com/in/jameswilson"
        }
    },
    {
        "name": "Lisa Zhang",
        "email": "<EMAIL>",
        "profile": {
            "role": "Data Scientist",
            "bio": "Data scientist with expertise in machine learning and statistical analysis.",
            "years_of_experience": 5,
            "is_available": True,
            "preferred_work_type": "Hybrid",
            "github": "https://github.com/lisazhang",
            "linkedin": "https://linkedin.com/in/lisazhang"
        }
    },
    {
        "name": "Alex Thompson",
        "email": "<EMAIL>",
        "profile": {
            "role": "UI/UX Developer",
            "bio": "UI/UX developer combining design thinking with technical implementation.",
            "years_of_experience": 3,
            "is_available": True,
            "preferred_work_type": "Remote",
            "github": "https://github.com/alexthompson",
            "linkedin": "https://linkedin.com/in/alexthompson"
        }
    },
    {
        "name": "Maria Garcia",
        "email": "<EMAIL>",
        "profile": {
            "role": "Cloud Engineer",
            "bio": "Cloud engineer specializing in AWS and multi-cloud architectures.",
            "years_of_experience": 7,
            "is_available": False,
            "preferred_work_type": "Remote",
            "github": "https://github.com/mariagarcia",
            "linkedin": "https://linkedin.com/in/mariagarcia"
        }
    },
    {
        "name": "John Smith",
        "email": "<EMAIL>",
        "profile": {
            "role": "Security Engineer",
            "bio": "Security engineer focused on application security and penetration testing.",
            "years_of_experience": 8,
            "is_available": True,
            "preferred_work_type": "On-site",
            "github": "https://github.com/johnsmith",
            "linkedin": "https://linkedin.com/in/johnsmith"
        }
    },
    {
        "name": "Anna Kim",
        "email": "<EMAIL>",
        "profile": {
            "role": "Machine Learning Engineer",
            "bio": "ML engineer specializing in computer vision and deep learning applications.",
            "years_of_experience": 4,
            "is_available": True,
            "preferred_work_type": "Hybrid",
            "github": "https://github.com/annakim",
            "linkedin": "https://linkedin.com/in/annakim"
        }
    }
]

def populate_talents():
    print("🌱 Starting to populate talents...")
    
    for dev in developers:
        # Add tech stack to profile based on role
        dev['profile']['tech_stack'] = tech_stacks[dev['profile']['role']]
        
        # Add random previous company
        dev['profile']['previous_company'] = random.choice(companies)
        
        # Create talent object
        talent = Talent.objects.create(
            name=dev['name'],
            email=dev['email'],
            profile=dev['profile'],
            featured_order=random.randint(1, 10) if random.random() > 0.7 else None  # 30% chance of being featured
        )
        print(f"✅ Created talent: {talent.name} - {talent.profile['role']}")

if __name__ == "__main__":
    print("Starting talent population script...")
    populate_talents()
    print("✨ Finished populating talents!")

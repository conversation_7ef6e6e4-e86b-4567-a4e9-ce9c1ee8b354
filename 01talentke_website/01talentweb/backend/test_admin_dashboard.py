#!/usr/bin/env python
"""
Simple test script to verify admin dashboard functionality.
Run this after setting up the admin dashboard.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zone01web.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse
import json

def test_admin_dashboard():
    """Test basic admin dashboard functionality."""
    
    print("🧪 Testing Admin Dashboard...")
    
    # Create test client
    client = Client()
    
    # Test 1: Check if admin login page loads
    print("\n1. Testing admin login page...")
    try:
        response = client.get('/admin-dashboard/login/')
        if response.status_code == 200:
            print("   ✅ Admin login page loads successfully")
        else:
            print(f"   ❌ Admin login page failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error loading admin login page: {e}")
    
    # Test 2: Create test superuser if doesn't exist
    print("\n2. Setting up test superuser...")
    try:
        test_user, created = User.objects.get_or_create(
            username='testadmin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            print("   ✅ Test superuser created")
        else:
            print("   ✅ Test superuser already exists")
    except Exception as e:
        print(f"   ❌ Error creating test superuser: {e}")
        return
    
    # Test 3: Test login functionality
    print("\n3. Testing login functionality...")
    try:
        login_successful = client.login(username='testadmin', password='testpass123')
        if login_successful:
            print("   ✅ Login successful")
        else:
            print("   ❌ Login failed")
            return
    except Exception as e:
        print(f"   ❌ Error during login: {e}")
        return
    
    # Test 4: Test dashboard access
    print("\n4. Testing dashboard access...")
    try:
        response = client.get('/admin-dashboard/')
        if response.status_code == 200:
            print("   ✅ Dashboard accessible")
        else:
            print(f"   ❌ Dashboard access failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing dashboard: {e}")
    
    # Test 5: Test talents page
    print("\n5. Testing talents page...")
    try:
        response = client.get('/admin-dashboard/talents/')
        if response.status_code == 200:
            print("   ✅ Talents page accessible")
        else:
            print(f"   ❌ Talents page access failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing talents page: {e}")
    
    # Test 6: Test hire requests page
    print("\n6. Testing hire requests page...")
    try:
        response = client.get('/admin-dashboard/hire-requests/')
        if response.status_code == 200:
            print("   ✅ Hire requests page accessible")
        else:
            print(f"   ❌ Hire requests page access failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing hire requests page: {e}")
    
    # Test 7: Test services page
    print("\n7. Testing services page...")
    try:
        response = client.get('/admin-dashboard/services/')
        if response.status_code == 200:
            print("   ✅ Services page accessible")
        else:
            print(f"   ❌ Services page access failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing services page: {e}")
    
    print("\n🎉 Admin Dashboard tests completed!")
    print("\n📝 Next steps:")
    print("   1. Start the development server: python manage.py runserver")
    print("   2. Visit: http://localhost:8000/admin-dashboard/login/")
    print("   3. Login with: testadmin / testpass123")
    print("   4. Explore the admin dashboard!")

if __name__ == '__main__':
    test_admin_dashboard()

from talent.models import Talent
import uuid
import random
from django.core.files.base import ContentFile
import requests

# Function to get a random profile image from UI Faces API
def get_random_profile_image():
    try:
        response = requests.get('https://i.pravatar.cc/300')
        if response.status_code == 200:
            return ContentFile(response.content, name=f'profile_{uuid.uuid4()}.jpg')
        return None
    except:
        return None

roles = [
    'Backend Developer', 'Frontend Developer', 'Full Stack Developer',
    'Data Scientist', 'DevOps Engineer', 'UI/UX Designer'
]
bios = [
    'Experienced in building scalable web applications.',
    'Expert in cloud infrastructure and automation.',
    'Passionate about data and analytics.',
    'Specialist in user experience and design.',
    'Loves solving complex problems.',
    'Team player and agile enthusiast.'
]
skills = [
    'Python', 'Django', 'Angular', 'AWS', 'Docker', 'React', 'Vue', 'Javascript', 
    'Golang', 'PHP', 'Rails', 'Rust', 'GCP', 'UI/UX', '<PERSON><PERSON>', 'Node.js', 'TypeScript',
    'MongoDB', 'PostgreSQL', 'Redis', 'Kubernetes', 'CI/CD', 'Git', 'REST API', 
    'GraphQL', 'Java', 'Spring Boot', 'MySQL', 'Linux', 'Nginx'
]

for i in range(1, 91):
    # Generate random number of skills (between 5 and 8)
    num_skills = random.randint(5, 8)
    # Sample unique skills randomly
    selected_skills = random.sample(skills, num_skills)
    
    # Create talent with profile image
    talent = Talent.objects.create(
        id=uuid.uuid4(),
        name=f'Talent {i}',
        email=f'talent{i}@example.com',
        profile={
            'role': random.choice(roles),
            'bio': random.choice(bios),
            'skills': selected_skills,
            'average_rating': round(random.uniform(3.5, 5.0), 2)
        }
    )
    
    # Attempt to add profile image
    profile_image = get_random_profile_image()
    if profile_image:
        talent.image = profile_image
        talent.save()

print('Created 90 talents.')

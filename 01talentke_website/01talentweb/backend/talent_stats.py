"""
Talent Profile Statistics and De<PERSON> Script

This script provides statistics and examples from the talent database
after running the bulk_create_talents.py script.

Usage:
    python manage.py shell < talent_stats.py
"""

from talent.models import Talent
from collections import Counter
import json

def show_talent_statistics():
    """Display comprehensive statistics about the talent profiles."""
    
    print("🚀 01 Talent Kenya - Profile Statistics")
    print("=" * 60)
    
    total_talents = Talent.objects.count()
    print(f"📊 Total Talent Profiles: {total_talents}")
    
    if total_talents == 0:
        print("❌ No talents found. Run bulk_create_talents.py first!")
        return
    
    print("\n" + "📋 ROLE DISTRIBUTION")
    print("-" * 40)
    
    # Get all roles and count them
    roles = [talent.profile.get('role', 'Unknown') for talent in Talent.objects.all()]
    role_counts = Counter(roles)
    
    for role, count in role_counts.most_common():
        percentage = (count / total_talents) * 100
        bar = "█" * int(percentage / 2)  # Simple progress bar
        print(f"{role:<30} {count:>3} ({percentage:>5.1f}%) {bar}")
    
    print(f"\n📈 Total Unique Roles: {len(role_counts)}")
    
    # Availability statistics
    print("\n" + "🟢 AVAILABILITY STATUS")
    print("-" * 40)
    
    available_count = Talent.objects.filter(profile__is_available=True).count()
    unavailable_count = total_talents - available_count
    
    available_percent = (available_count / total_talents) * 100
    unavailable_percent = (unavailable_count / total_talents) * 100
    
    print(f"Available:    {available_count:>3} ({available_percent:>5.1f}%)")
    print(f"Unavailable:  {unavailable_count:>3} ({unavailable_percent:>5.1f}%)")
    
    # Skills analysis
    print("\n" + "🛠️  SKILLS ANALYSIS")
    print("-" * 40)
    
    all_skills = []
    for talent in Talent.objects.all():
        skills = talent.profile.get('skills', [])
        all_skills.extend(skills)
    
    skill_counts = Counter(all_skills)
    print(f"Total Skills Mentioned: {len(all_skills)}")
    print(f"Unique Skills: {len(skill_counts)}")
    
    print("\nTop 10 Most Common Skills:")
    for skill, count in skill_counts.most_common(10):
        percentage = (count / len(all_skills)) * 100
        print(f"  {skill:<25} {count:>3} ({percentage:>5.1f}%)")
    
    # Location distribution
    print("\n" + "📍 LOCATION DISTRIBUTION")
    print("-" * 40)
    
    locations = [talent.profile.get('location', 'Unknown') for talent in Talent.objects.all()]
    location_counts = Counter(locations)
    
    for location, count in location_counts.most_common():
        percentage = (count / total_talents) * 100
        print(f"{location:<20} {count:>3} ({percentage:>5.1f}%)")
    
    # Experience and rating analysis
    print("\n" + "📊 EXPERIENCE & RATINGS")
    print("-" * 40)
    
    experience_years = [talent.profile.get('experience_years', 0) for talent in Talent.objects.all()]
    ratings = [talent.profile.get('average_rating', 0) for talent in Talent.objects.all()]
    
    if experience_years:
        avg_experience = sum(experience_years) / len(experience_years)
        min_exp = min(experience_years)
        max_exp = max(experience_years)
        print(f"Experience (years): Min: {min_exp}, Max: {max_exp}, Avg: {avg_experience:.1f}")
    
    if ratings:
        avg_rating = sum(ratings) / len(ratings)
        min_rating = min(ratings)
        max_rating = max(ratings)
        print(f"Ratings (1-5):      Min: {min_rating}, Max: {max_rating}, Avg: {avg_rating:.1f}")


def show_sample_profiles(count=3):
    """Display sample talent profiles."""
    
    print("\n" + "👥 SAMPLE TALENT PROFILES")
    print("=" * 60)
    
    talents = Talent.objects.all()[:count]
    
    for i, talent in enumerate(talents, 1):
        print(f"\n{i}. {talent.name}")
        print(f"   📧 {talent.email}")
        print(f"   💼 {talent.profile.get('role', 'Unknown Role')}")
        print(f"   📍 {talent.profile.get('location', 'Unknown Location')}")
        print(f"   ⭐ {talent.profile.get('average_rating', 'N/A')} rating")
        print(f"   💰 ${talent.profile.get('hourly_rate', 'N/A')}/hour")
        print(f"   🎯 {talent.profile.get('experience_years', 'N/A')} years experience")
        print(f"   🟢 {'Available' if talent.profile.get('is_available', False) else 'Unavailable'}")
        
        skills = talent.profile.get('skills', [])
        if skills:
            print(f"   🛠️  Skills: {', '.join(skills[:5])}")  # Show first 5 skills
            if len(skills) > 5:
                print(f"      ...and {len(skills) - 5} more")
        
        bio = talent.profile.get('bio', '')
        if bio:
            # Truncate bio to first 100 characters
            short_bio = bio[:100] + "..." if len(bio) > 100 else bio
            print(f"   📝 {short_bio}")
        
        print("-" * 60)


def export_role_summary():
    """Export a summary of roles and their characteristics."""
    
    print("\n" + "📤 ROLE SUMMARY EXPORT")
    print("=" * 60)
    
    role_summary = {}
    
    for talent in Talent.objects.all():
        role = talent.profile.get('role', 'Unknown')
        
        if role not in role_summary:
            role_summary[role] = {
                'count': 0,
                'common_skills': [],
                'avg_experience': 0,
                'avg_rating': 0,
                'avg_rate': 0
            }
        
        role_summary[role]['count'] += 1
        role_summary[role]['common_skills'].extend(talent.profile.get('skills', []))
        role_summary[role]['avg_experience'] += talent.profile.get('experience_years', 0)
        role_summary[role]['avg_rating'] += talent.profile.get('average_rating', 0)
        role_summary[role]['avg_rate'] += talent.profile.get('hourly_rate', 0)
    
    # Calculate averages and most common skills
    for role, data in role_summary.items():
        if data['count'] > 0:
            data['avg_experience'] = round(data['avg_experience'] / data['count'], 1)
            data['avg_rating'] = round(data['avg_rating'] / data['count'], 1)
            data['avg_rate'] = round(data['avg_rate'] / data['count'])
            
            # Get top 3 most common skills for this role
            skill_counts = Counter(data['common_skills'])
            data['top_skills'] = [skill for skill, count in skill_counts.most_common(3)]
            del data['common_skills']  # Remove the full list
    
    print("Role summary generated. Here are the top 5 roles:")
    
    sorted_roles = sorted(role_summary.items(), key=lambda x: x[1]['count'], reverse=True)
    
    for role, data in sorted_roles[:5]:
        print(f"\n🎯 {role}")
        print(f"   Count: {data['count']}")
        print(f"   Avg Experience: {data['avg_experience']} years")
        print(f"   Avg Rating: {data['avg_rating']}/5.0")
        print(f"   Avg Rate: ${data['avg_rate']}/hour")
        print(f"   Top Skills: {', '.join(data['top_skills'])}")


def main():
    """Main function to run all statistics."""
    show_talent_statistics()
    show_sample_profiles(3)
    export_role_summary()
    
    print("\n" + "✅ Statistics Complete!")
    print("🚀 Ready to showcase 01 Talent Kenya profiles!")


if __name__ == "__main__":
    main()
else:
    # If imported or run in Django shell
    main()

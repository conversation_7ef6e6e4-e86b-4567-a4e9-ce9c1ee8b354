from inertia import inertia
from services.models import Service
from services.views import get_navbar_services, serialize_service

@inertia('ServiceTemplate/Index')
def service_template(request):
    service_name = request.GET.get('service')
    language = request.GET.get('language')
    framework = request.GET.get('framework')
    specialization = request.GET.get('specialization')
    
    service = None
    
    if service_name:
        service = Service.objects.filter(name__iexact=service_name).first()
    elif language:
        service = Service.objects.filter(name__iexact=language, category='language').first()
    elif framework:
        service = Service.objects.filter(name__iexact=framework, category='framework').first()
    elif specialization:
        service = Service.objects.filter(name__iexact=specialization, category='specialization').first()
    
    return {
        'service': serialize_service(service, request) if service else None,
        'navbar_services': get_navbar_services(request)
    }

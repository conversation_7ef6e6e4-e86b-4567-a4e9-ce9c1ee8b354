# fly.toml app configuration file generated for 01talentke-website on 2025-07-04T09:45:08+03:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = '01talentke-website'
primary_region = 'jnb'

[build]
  dockerfile = '01talentweb/Dockerfile'

[deploy]
  release_command = 'sh -c "python manage.py migrate && python manage.py collectstatic --noinput"'

[env]
  DEBUG = 'False'
  DJANGO_SETTINGS_MODULE = 'zone01web.settings'
  IN_DOCKER = 'True'
  PORT = '8000'

[processes]
  app = 'gunicorn --workers 2 --bind 0.0.0.0:8000 --timeout 120 --access-logfile - --error-logfile - --log-level info zone01web.wsgi:application'

[[mounts]]
  source = 'talentke_media'
  destination = '/app/media'

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

# Temporarily disabled health check
  # [[http_service.checks]]
  #   interval = '30s'
  #   timeout = '5s'
  #   grace_period = '10s'
  #   method = 'GET'
  #   path = '/health/'

[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 512

[[statics]]
  guest_path = '/app/staticfiles'
  url_prefix = '/static/'

[[statics]]
  guest_path = '/app/media'
  url_prefix = '/media/'

# Stage 1: Build frontend
FROM node:20-slim AS frontend-builder
WORKDIR /app/frontend
COPY 01talentweb/frontend/package*.json ./
RUN npm install
COPY 01talentweb/frontend/ ./
RUN npm run vite-build
# Build Tailwind CSS
RUN npx tailwindcss -i ./static/css/main.css -o ./static/dist/css/app.css --minify

# Stage 2: Python application
FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBUG=False \
    IN_DOCKER=True

# Set work directory
WORKDIR /app

# Install system dependencies including curl for healthcheck
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    netcat-traditional \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY 01talentweb/backend/requirements.txt ./
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir gunicorn

# Create required directories with proper structure
RUN mkdir -p staticfiles media frontend/dist frontend/static

# Copy backend files (this copies the entire backend directory structure)
COPY 01talentweb/backend/ ./

# Copy frontend static files if they exist
COPY 01talentweb/frontend/static ./frontend/static

# Copy frontend build from builder stage
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# Copy the built Tailwind CSS from frontend-builder stage
COPY --from=frontend-builder /app/frontend/static/dist ./frontend/static/dist

# Copy environment file (optional for Fly.io - will use env vars instead)
COPY 01talentweb/backend/.env.prod* ./

# Collect static files during build (with dummy environment variables)
ENV SECRET_KEY=dummy-key-for-collectstatic
ENV DATABASE_URL=sqlite:///dummy.db
RUN python manage.py collectstatic --noinput

# Expose port
EXPOSE 8000

# Copy and set up entrypoint script
COPY 01talentweb/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Start using entrypoint script
CMD ["/app/entrypoint.sh"]
# 🔍 Merge Conflict Analysis - Main → ProductionV2

## Critical Issues Identified

### ⚠️ MAJOR: settings.py Deployment Configurations Lost in Main

The main branch has **reverted critical Fly.io deployment configurations** in `settings.py`:

**Missing in Main Branch:**
```python
# Check if running in Docker or Fly.io
IN_DOCKER = os.getenv('IN_DOCKER', 'False').lower() == 'true'
ON_FLY = os.getenv('FLY_APP_NAME') is not None

if IN_DOCKER or ON_FLY:
    # In Docker or Fly.io, the templates are in the working directory
    BASE_DIR = Path('/app')

# Fly.io specific settings
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Add Fly.io hosts
if ON_FLY:
    ALLOWED_HOSTS.extend([
        f"{os.getenv('FLY_APP_NAME')}.fly.dev",
        f"{os.getenv('FLY_APP_NAME')}.flycast",
    ])

# CSRF trusted origins for production
CSRF_TRUSTED_ORIGINS = []
if ON_FLY:
    CSRF_TRUSTED_ORIGINS.extend([
        f"https://{os.getenv('FLY_APP_NAME')}.fly.dev",
        f"https://{os.getenv('FLY_APP_NAME')}.flycast",
    ])

# Static files storage
"staticfiles": {
    "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
},

# Vite dev mode disabled for production
DJANGO_VITE_DEV_MODE = False
```

**Reverted to in Main:**
```python
BASE_DIR = Path(__file__).resolve().parent.parent.parent  # Wrong for Docker!
DEBUG = True  # DANGEROUS for production!
ALLOWED_HOSTS = ['*']  # Insecure!
# Missing all Fly.io configurations
```

## Strategic Merge Plan

### Option 1: Preserve Deployment Settings (RECOMMENDED)
1. Merge main into productionv2
2. **Immediately restore** deployment-critical settings from backup
3. Test and deploy

### Option 2: Manual Cherry-Pick
1. Cherry-pick specific commits from main
2. Skip commits that modify settings.py
3. Manually apply content changes without breaking deployment

### Files Requiring Special Attention

1. **settings.py** - Must preserve productionv2 version
2. **fly.toml** - Keep productionv2 version
3. **Dockerfile** - Keep productionv2 version  
4. **entrypoint.sh** - Keep productionv2 version

### Safe Files to Merge
- Frontend components and pages
- Templates and static files
- New models and migrations
- Content updates and styling changes

## Recommended Merge Strategy

```bash
# 1. Start from productionv2
git checkout productionv2

# 2. Merge main (will have conflicts)
git merge main

# 3. Resolve conflicts by keeping productionv2 versions of:
#    - settings.py (entire file)
#    - fly.toml
#    - Dockerfile
#    - entrypoint.sh

# 4. Accept main versions for:
#    - Frontend files
#    - Templates
#    - Content updates
#    - New features
```

## Risk Assessment

**HIGH RISK**: Direct merge without conflict resolution
**MEDIUM RISK**: Merge with proper conflict resolution
**LOW RISK**: Cherry-pick approach (slower but safer)

## Next Steps

1. Perform strategic merge with conflict resolution
2. Restore deployment configurations immediately
3. Test locally before deploying
4. Deploy with monitoring

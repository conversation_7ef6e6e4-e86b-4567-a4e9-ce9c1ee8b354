# 🚨 ROLLBACK PLAN - 01TalentKE Website Deployment

## Emergency Rollback Procedures

### Quick Rollback Commands

If deployment fails or issues are discovered:

```bash
# 1. Switch back to backup branch
git checkout productionv2-backup-$(date +%Y%m%d)-*

# 2. Force push to productionv2 (EMERGENCY ONLY)
git checkout productionv2
git reset --hard productionv2-backup-$(date +%Y%m%d)-*
git push origin productionv2 --force

# 3. Redeploy previous working version
flyctl deploy
```

### Restore Configuration Files

If only configuration files need to be restored:

```bash
# Restore critical deployment files
cp deployment-backup/productionv2-configs/fly.toml ./
cp deployment-backup/productionv2-configs/Dockerfile 01talentweb/
cp deployment-backup/productionv2-configs/settings.py 01talentweb/backend/zone01web/
cp deployment-backup/productionv2-configs/entrypoint.sh 01talentweb/

# Commit and deploy
git add .
git commit -m "ROLLBACK: Restore deployment configurations"
flyctl deploy
```

### Fly.io Rollback Commands

```bash
# Check deployment history
flyctl releases

# Rollback to previous release
flyctl releases rollback <release-id>

# Check app status
flyctl status
flyctl logs
```

### Database Rollback (if needed)

```bash
# List database backups
flyctl postgres backup list -a 01talentke-db

# Restore from backup (DESTRUCTIVE - use with caution)
flyctl postgres backup restore <backup-id> -a 01talentke-db
```

## Pre-Merge Backup Status

✅ **Backup Branch Created**: `productionv2-backup-$(date +%Y%m%d)-*`
✅ **Configuration Files Backed Up**: 
- fly.toml
- Dockerfile  
- settings.py
- entrypoint.sh

## Critical Settings to Preserve

### settings.py
- `ON_FLY = os.getenv('FLY_APP_NAME') is not None`
- `DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'`
- `ALLOWED_HOSTS` with Fly.io domains
- `CSRF_TRUSTED_ORIGINS` for Fly.io
- `CompressedStaticFilesStorage` backend
- `DJANGO_VITE_DEV_MODE = False`

### fly.toml
- App name: `01talentke-website`
- Region: `jnb`
- Release command with migrations
- Volume mounts for media files
- Static file serving configuration

## Contact Information

If rollback is needed, document:
- Time of rollback: ___________
- Reason: ___________________
- Commands used: ____________
- Result: ___________________
